.status-container {
  min-height: 35px;
  min-width: 100px;
  background-color: rgb(62,62,62);
  border-radius: 8%;
  display:flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: center;
  gap: 8px;
}

.status-circle-default {
  height: 25px;
  width: 25px;
  background-color: rgb(108,108,108);
  border-radius: 50%;
}

.status-circle-green {
  height: 25px;
  width: 25px;
  background-color: rgb(161, 116, 11);
  border-radius: 50%;
}

.status-circle-yellow {
  height: 25px;
  width: 25px;
  background-color: rgb(161, 116, 11);
  border-radius: 50%;
}

.status-circle-red {
  height: 25px;
  width: 25px;
  background-color: rgb(161,21,47);
  border-radius: 50%;
}


/* Start MenuModal */
/deep/ .menuModel ul li:nth-child(4n+1) {
  background: #8D99A6;
  color: white;
}

/deep/ .menuModel ul li:nth-child(1n+1) a span:nth-child(1) {
  color: rgb(161,21,47);
}

/deep/ .menuModel ul li:nth-child(2n+1) a span:nth-child(1) {
  color: rgb(161, 116, 11);
}

/deep/ .menuModel ul li:nth-child(11) a span:nth-child(1) {
  color: rgb(161, 116, 11);
}

/deep/ .menuModel ul li:nth-child(4) a span:nth-child(1) {
  color: rgb(20, 135, 49);
}

/deep/ .menuModel ul li:nth-child(8) a span:nth-child(1) {
  color: rgb(20, 135, 49);
}

/deep/ .menuModel ul li:nth-child(12) a span:nth-child(1) {
  color: rgb(20, 135, 49);
}

/deep/ .menuModel ul li.active {
  background: #1677aa;
}
/deep/ .menuModel ul {
  background: rgb(247, 247, 247);
  box-shadow: 0 2px 4px 0 rgba(0,0,0,1);
}

/deep/ .menuModel ul li.active a span:nth-child(2) {
  color: #fff;
}
/* End MenuModal */
