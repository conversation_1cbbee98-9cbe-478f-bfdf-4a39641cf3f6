import { Injectable } from '@angular/core';
import {ApiServiceAbstract} from './api-service.abstract';
import {Observable} from 'rxjs';
import {TagDto} from '../api/models/tag/tag-dto';
import {CompanyDto} from '../api/models/company/company-dto';
import {ResponsibleUserDto} from '../api/models/user/responsible-user-dto';
import {MachineTypeDto} from '../api/models/machine-type/machine-type-dto';

@Injectable({
  providedIn: 'root'
})
export class OptionsService extends ApiServiceAbstract{

  constructor(
  ) {
    super();
  }

  public getTagOptions(): Observable<TagDto[]>{
    return this.getForObject<TagDto[]>('/tags?page=0&size=9999&sort=name,asc', {});
  }

  public getCompanyOptions(): Observable<CompanyDto[]>{
    return this.getForObject<CompanyDto[]>('/companies?page=0&size=9999&sort=name,asc', {});
  }

  public getUserOptions(): Observable<ResponsibleUserDto[]>{
    return this.getForObject<ResponsibleUserDto[]>('/users?page=0&size=9999&sort=login,asc', {});
  }

  public getTypeOptions(): Observable<MachineTypeDto[]>{
    return this.getForObject<MachineTypeDto[]>('/machineTypes?page=0&size=9999&sort=name,asc', {});
  }

}
