import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpResponse} from '@angular/common/http';
import {environment} from '../../environments/environment';
import {LoginRequest} from '../api/models/auth/login-request-dto';
import {ApiServiceAbstract} from './api-service.abstract';

const httpOptions = {
    headers: new HttpHeaders({ 'Content-Type': 'application/json' }),
    observe: 'response' as 'response'
};
const API_URL = environment.apiUrl;

@Injectable({
    providedIn: 'root'
})
export class TokenService extends ApiServiceAbstract {

  constructor(private http: HttpClient) { super(); }
    public getResponseHeaders(loginRequest: LoginRequest) {
        return this.postForObjectRaw<any>(`/authenticate`, loginRequest, {});
    }

    public logout() {
        const logoutUrl = API_URL + '/logout';
        return this.http.get(logoutUrl, {responseType: 'text'});
        return true;
    }


}
