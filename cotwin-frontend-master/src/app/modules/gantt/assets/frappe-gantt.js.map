{"version": 3, "file": "frappe-gantt.js", "sources": ["../src/date_utils.js", "../src/svg_utils.js", "../src/bar.js", "../src/arrow.js", "../src/popup.js", "../src/index.js"], "sourcesContent": ["const YEAR = 'year';\nconst MONTH = 'month';\nconst DAY = 'day';\nconst HOUR = 'hour';\nconst MINUTE = 'minute';\nconst SECOND = 'second';\nconst MILLISECOND = 'millisecond';\n\nconst month_names = {\n    en: [\n        'January',\n        'February',\n        'March',\n        'April',\n        'May',\n        'June',\n        'July',\n        'August',\n        'September',\n        'October',\n        'November',\n        'December',\n    ],\n    es: [\n        'Enero',\n        'Febrero',\n        '<PERSON><PERSON>',\n        '<PERSON>bri<PERSON>',\n        '<PERSON>',\n        '<PERSON><PERSON>',\n        '<PERSON>',\n        'Agosto',\n        'Septiembre',\n        'Octubre',\n        'Noviembre',\n        'Diciembre',\n    ],\n    ru: [\n        'Январь',\n        'Февраль',\n        'Март',\n        'Апрель',\n        'Май',\n        'Июнь',\n        'Июль',\n        'Август',\n        'Сентябрь',\n        'Октябрь',\n        'Ноябрь',\n        'Декабрь',\n    ],\n    ptBr: [\n        '<PERSON>',\n        '<PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON><PERSON>',\n        '<PERSON><PERSON>',\n        '<PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON><PERSON><PERSON>',\n        '<PERSON>zembro',\n    ],\n    fr: [\n        '<PERSON>vier',\n        'F<PERSON>vrier',\n        '<PERSON>',\n        'Avril',\n        '<PERSON>',\n        '<PERSON>in',\n        '<PERSON>illet',\n        'Août',\n        'Septem<PERSON>',\n        'Octobre',\n        '<PERSON>em<PERSON>',\n        '<PERSON>écembre',\n    ],\n    tr: [\n        'Ocak',\n        '<PERSON>ubat',\n        '<PERSON>',\n        '<PERSON>san',\n        'Mayıs',\n        'Haziran',\n        'Temmuz',\n        'Ağustos',\n        'Eylül',\n        'Ekim',\n        'Kasım',\n        'Aralık',\n    ],\n    zh: [\n        '一月',\n        '二月',\n        '三月',\n        '四月',\n        '五月',\n        '六月',\n        '七月',\n        '八月',\n        '九月',\n        '十月',\n        '十一月',\n        '十二月',\n    ],\n};\n\nexport default {\n    parse(date, date_separator = '-', time_separator = /[.:]/) {\n        if (date instanceof Date) {\n            return date;\n        }\n        if (typeof date === 'string') {\n            let date_parts, time_parts;\n            const parts = date.split(' ');\n\n            date_parts = parts[0]\n                .split(date_separator)\n                .map((val) => parseInt(val, 10));\n            time_parts = parts[1] && parts[1].split(time_separator);\n\n            // month is 0 indexed\n            date_parts[1] = date_parts[1] - 1;\n\n            let vals = date_parts;\n\n            if (time_parts && time_parts.length) {\n                if (time_parts.length == 4) {\n                    time_parts[3] = '0.' + time_parts[3];\n                    time_parts[3] = parseFloat(time_parts[3]) * 1000;\n                }\n                vals = vals.concat(time_parts);\n            }\n\n            return new Date(...vals);\n        }\n    },\n\n    to_string(date, with_time = false) {\n        if (!(date instanceof Date)) {\n            throw new TypeError('Invalid argument type');\n        }\n        const vals = this.get_date_values(date).map((val, i) => {\n            if (i === 1) {\n                // add 1 for month\n                val = val + 1;\n            }\n\n            if (i === 6) {\n                return padStart(val + '', 3, '0');\n            }\n\n            return padStart(val + '', 2, '0');\n        });\n        const date_string = `${vals[0]}-${vals[1]}-${vals[2]}`;\n        const time_string = `${vals[3]}:${vals[4]}:${vals[5]}.${vals[6]}`;\n\n        return date_string + (with_time ? ' ' + time_string : '');\n    },\n\n    format(date, format_string = 'YYYY-MM-DD HH:mm:ss.SSS', lang = 'en') {\n        const values = this.get_date_values(date).map((d) => padStart(d, 2, 0));\n        const format_map = {\n            YYYY: values[0],\n            MM: padStart(+values[1] + 1, 2, 0),\n            DD: values[2],\n            HH: values[3],\n            mm: values[4],\n            ss: values[5],\n            SSS: values[6],\n            D: values[2],\n            MMMM: month_names[lang][+values[1]],\n            MMM: month_names[lang][+values[1]],\n        };\n\n        let str = format_string;\n        const formatted_values = [];\n\n        Object.keys(format_map)\n            .sort((a, b) => b.length - a.length) // big string first\n            .forEach((key) => {\n                if (str.includes(key)) {\n                    str = str.replace(key, `$${formatted_values.length}`);\n                    formatted_values.push(format_map[key]);\n                }\n            });\n\n        formatted_values.forEach((value, i) => {\n            str = str.replace(`$${i}`, value);\n        });\n\n        return str;\n    },\n\n    diff(date_a, date_b, scale = DAY) {\n        let milliseconds, seconds, hours, minutes, days, months, years;\n\n        milliseconds = date_a - date_b;\n        seconds = milliseconds / 1000;\n        minutes = seconds / 60;\n        hours = minutes / 60;\n        days = hours / 24;\n        months = days / 30;\n        years = months / 12;\n\n        if (!scale.endsWith('s')) {\n            scale += 's';\n        }\n\n        return Math.floor(\n            {\n                milliseconds,\n                seconds,\n                minutes,\n                hours,\n                days,\n                months,\n                years,\n            }[scale]\n        );\n    },\n\n    today() {\n        const vals = this.get_date_values(new Date()).slice(0, 3);\n        return new Date(...vals);\n    },\n\n    now() {\n        return new Date();\n    },\n\n    add(date, qty, scale) {\n        qty = parseInt(qty, 10);\n        const vals = [\n            date.getFullYear() + (scale === YEAR ? qty : 0),\n            date.getMonth() + (scale === MONTH ? qty : 0),\n            date.getDate() + (scale === DAY ? qty : 0),\n            date.getHours() + (scale === HOUR ? qty : 0),\n            date.getMinutes() + (scale === MINUTE ? qty : 0),\n            date.getSeconds() + (scale === SECOND ? qty : 0),\n            date.getMilliseconds() + (scale === MILLISECOND ? qty : 0),\n        ];\n        return new Date(...vals);\n    },\n\n    start_of(date, scale) {\n        const scores = {\n            [YEAR]: 6,\n            [MONTH]: 5,\n            [DAY]: 4,\n            [HOUR]: 3,\n            [MINUTE]: 2,\n            [SECOND]: 1,\n            [MILLISECOND]: 0,\n        };\n\n        function should_reset(_scale) {\n            const max_score = scores[scale];\n            return scores[_scale] <= max_score;\n        }\n\n        const vals = [\n            date.getFullYear(),\n            should_reset(YEAR) ? 0 : date.getMonth(),\n            should_reset(MONTH) ? 1 : date.getDate(),\n            should_reset(DAY) ? 0 : date.getHours(),\n            should_reset(HOUR) ? 0 : date.getMinutes(),\n            should_reset(MINUTE) ? 0 : date.getSeconds(),\n            should_reset(SECOND) ? 0 : date.getMilliseconds(),\n        ];\n\n        return new Date(...vals);\n    },\n\n    clone(date) {\n        return new Date(...this.get_date_values(date));\n    },\n\n    get_date_values(date) {\n        return [\n            date.getFullYear(),\n            date.getMonth(),\n            date.getDate(),\n            date.getHours(),\n            date.getMinutes(),\n            date.getSeconds(),\n            date.getMilliseconds(),\n        ];\n    },\n\n    get_days_in_month(date) {\n        const no_of_days = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\n        const month = date.getMonth();\n\n        if (month !== 1) {\n            return no_of_days[month];\n        }\n\n        // Feb\n        const year = date.getFullYear();\n        if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {\n            return 29;\n        }\n        return 28;\n    },\n};\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/padStart\nfunction padStart(str, targetLength, padString) {\n    str = str + '';\n    targetLength = targetLength >> 0;\n    padString = String(typeof padString !== 'undefined' ? padString : ' ');\n    if (str.length > targetLength) {\n        return String(str);\n    } else {\n        targetLength = targetLength - str.length;\n        if (targetLength > padString.length) {\n            padString += padString.repeat(targetLength / padString.length);\n        }\n        return padString.slice(0, targetLength) + String(str);\n    }\n}\n", "export function $(expr, con) {\n    return typeof expr === 'string'\n        ? (con || document).querySelector(expr)\n        : expr || null;\n}\n\nexport function createSVG(tag, attrs) {\n    const elem = document.createElementNS('http://www.w3.org/2000/svg', tag);\n    for (let attr in attrs) {\n        if (attr === 'append_to') {\n            const parent = attrs.append_to;\n            parent.appendChild(elem);\n        } else if (attr === 'innerHTML') {\n            elem.innerHTML = attrs.innerHTML;\n        } else {\n            elem.setAttribute(attr, attrs[attr]);\n        }\n    }\n    return elem;\n}\n\nexport function animateSVG(svgElement, attr, from, to) {\n    const animatedSvgElement = getAnimationElement(svgElement, attr, from, to);\n\n    if (animatedSvgElement === svgElement) {\n        // triggered 2nd time programmatically\n        // trigger artificial click event\n        const event = document.createEvent('HTMLEvents');\n        event.initEvent('click', true, true);\n        event.eventName = 'click';\n        animatedSvgElement.dispatchEvent(event);\n    }\n}\n\nfunction getAnimationElement(\n    svgElement,\n    attr,\n    from,\n    to,\n    dur = '0.4s',\n    begin = '0.1s'\n) {\n    const animEl = svgElement.querySelector('animate');\n    if (animEl) {\n        $.attr(animEl, {\n            attributeName: attr,\n            from,\n            to,\n            dur,\n            begin: 'click + ' + begin, // artificial click\n        });\n        return svgElement;\n    }\n\n    const animateElement = createSVG('animate', {\n        attributeName: attr,\n        from,\n        to,\n        dur,\n        begin,\n        calcMode: 'spline',\n        values: from + ';' + to,\n        keyTimes: '0; 1',\n        keySplines: cubic_bezier('ease-out'),\n    });\n    svgElement.appendChild(animateElement);\n\n    return svgElement;\n}\n\nfunction cubic_bezier(name) {\n    return {\n        ease: '.25 .1 .25 1',\n        linear: '0 0 1 1',\n        'ease-in': '.42 0 1 1',\n        'ease-out': '0 0 .58 1',\n        'ease-in-out': '.42 0 .58 1',\n    }[name];\n}\n\n$.on = (element, event, selector, callback) => {\n    if (!callback) {\n        callback = selector;\n        $.bind(element, event, callback);\n    } else {\n        $.delegate(element, event, selector, callback);\n    }\n};\n\n$.off = (element, event, handler) => {\n    element.removeEventListener(event, handler);\n};\n\n$.bind = (element, event, callback) => {\n    event.split(/\\s+/).forEach(function (event) {\n        element.addEventListener(event, callback);\n    });\n};\n\n$.delegate = (element, event, selector, callback) => {\n    element.addEventListener(event, function (e) {\n        const delegatedTarget = e.target.closest(selector);\n        if (delegatedTarget) {\n            e.delegatedTarget = delegatedTarget;\n            callback.call(this, e, delegatedTarget);\n        }\n    });\n};\n\n$.closest = (selector, element) => {\n    if (!element) return null;\n\n    if (element.matches(selector)) {\n        return element;\n    }\n\n    return $.closest(selector, element.parentNode);\n};\n\n$.attr = (element, attr, value) => {\n    if (!value && typeof attr === 'string') {\n        return element.getAttribute(attr);\n    }\n\n    if (typeof attr === 'object') {\n        for (let key in attr) {\n            $.attr(element, key, attr[key]);\n        }\n        return;\n    }\n\n    element.setAttribute(attr, value);\n};\n", "import date_utils from './date_utils';\nimport { $, createSVG, animateSVG } from './svg_utils';\n\nexport default class Bar {\n    constructor(gantt, task) {\n        this.set_defaults(gantt, task);\n        this.prepare();\n        this.draw();\n        this.bind();\n    }\n\n    set_defaults(gantt, task) {\n        this.action_completed = false;\n        this.gantt = gantt;\n        this.task = task;\n    }\n\n    prepare() {\n        this.prepare_values();\n        this.prepare_helpers();\n    }\n\n    prepare_values() {\n        this.invalid = this.task.invalid;\n        this.height = this.gantt.options.bar_height;\n        this.x = this.compute_x();\n        this.y = this.compute_y();\n        this.corner_radius = this.gantt.options.bar_corner_radius;\n        this.duration =\n            date_utils.diff(this.task._end, this.task._start, 'hour') /\n            this.gantt.options.step;\n        this.width = this.gantt.options.column_width * this.duration;\n        this.progress_width =\n            this.gantt.options.column_width *\n                this.duration *\n                (this.task.progress / 100) || 0;\n        this.group = createSVG('g', {\n            class: 'bar-wrapper ' + (this.task.custom_class || ''),\n            'data-id': this.task.id,\n        });\n        this.bar_group = createSVG('g', {\n            class: 'bar-group',\n            append_to: this.group,\n        });\n        this.handle_group = createSVG('g', {\n            class: 'handle-group',\n            append_to: this.group,\n        });\n    }\n\n    prepare_helpers() {\n        SVGElement.prototype.getX = function () {\n            return +this.getAttribute('x');\n        };\n        SVGElement.prototype.getY = function () {\n            return +this.getAttribute('y');\n        };\n        SVGElement.prototype.getWidth = function () {\n            return +this.getAttribute('width');\n        };\n        SVGElement.prototype.getHeight = function () {\n            return +this.getAttribute('height');\n        };\n        SVGElement.prototype.getEndX = function () {\n            return this.getX() + this.getWidth();\n        };\n    }\n\n    draw() {\n        this.draw_bar();\n        this.draw_progress_bar();\n        this.draw_label();\n        this.draw_resize_handles();\n    }\n\n    draw_bar() {\n        this.$bar = createSVG('rect', {\n            x: this.x,\n            y: this.y,\n            width: this.width,\n            height: this.height,\n            rx: this.corner_radius,\n            ry: this.corner_radius,\n            class: 'bar',\n            append_to: this.bar_group,\n        });\n\n        animateSVG(this.$bar, 'width', 0, this.width);\n\n        if (this.invalid) {\n            this.$bar.classList.add('bar-invalid');\n        }\n    }\n\n    draw_progress_bar() {\n        if (this.invalid) return;\n        this.$bar_progress = createSVG('rect', {\n            x: this.x,\n            y: this.y,\n            width: this.progress_width,\n            height: this.height,\n            rx: this.corner_radius,\n            ry: this.corner_radius,\n            class: 'bar-progress',\n            append_to: this.bar_group,\n        });\n\n        animateSVG(this.$bar_progress, 'width', 0, this.progress_width);\n    }\n\n    draw_label() {\n        createSVG('text', {\n            x: this.x + this.width / 2,\n            y: this.y + this.height / 2,\n            innerHTML: this.task.name,\n            class: 'bar-label',\n            append_to: this.bar_group,\n        });\n        // labels get BBox in the next tick\n        requestAnimationFrame(() => this.update_label_position());\n    }\n\n    draw_resize_handles() {\n        if (this.invalid) return;\n\n        const bar = this.$bar;\n        const handle_width = 8;\n\n        createSVG('rect', {\n            x: bar.getX() + bar.getWidth() - 9,\n            y: bar.getY() + 1,\n            width: handle_width,\n            height: this.height - 2,\n            rx: this.corner_radius,\n            ry: this.corner_radius,\n            class: 'handle right',\n            append_to: this.handle_group,\n        });\n\n        createSVG('rect', {\n            x: bar.getX() + 1,\n            y: bar.getY() + 1,\n            width: handle_width,\n            height: this.height - 2,\n            rx: this.corner_radius,\n            ry: this.corner_radius,\n            class: 'handle left',\n            append_to: this.handle_group,\n        });\n\n        if (this.task.progress && this.task.progress < 100) {\n            this.$handle_progress = createSVG('polygon', {\n                points: this.get_progress_polygon_points().join(','),\n                class: 'handle progress',\n                append_to: this.handle_group,\n            });\n        }\n    }\n\n    get_progress_polygon_points() {\n        const bar_progress = this.$bar_progress;\n        return [\n            bar_progress.getEndX() - 5,\n            bar_progress.getY() + bar_progress.getHeight(),\n            bar_progress.getEndX() + 5,\n            bar_progress.getY() + bar_progress.getHeight(),\n            bar_progress.getEndX(),\n            bar_progress.getY() + bar_progress.getHeight() - 8.66,\n        ];\n    }\n\n    bind() {\n        if (this.invalid) return;\n        this.setup_click_event();\n    }\n\n    setup_click_event() {\n        $.on(this.group, 'focus ' + this.gantt.options.popup_trigger, (e) => {\n            if (this.action_completed) {\n                // just finished a move action, wait for a few seconds\n                return;\n            }\n\n            this.show_popup();\n            this.gantt.unselect_all();\n            this.group.classList.add('active');\n        });\n\n        $.on(this.group, 'dblclick', (e) => {\n            if (this.action_completed) {\n                // just finished a move action, wait for a few seconds\n                return;\n            }\n\n            this.gantt.trigger_event('click', [this.task]);\n        });\n    }\n\n    show_popup() {\n        if (this.gantt.bar_being_dragged) return;\n\n        const start_date = date_utils.format(\n            this.task._start,\n            'MMM D',\n            this.gantt.options.language\n        );\n        const end_date = date_utils.format(\n            date_utils.add(this.task._end, -1, 'second'),\n            'MMM D',\n            this.gantt.options.language\n        );\n        const subtitle = start_date + ' - ' + end_date;\n\n        this.gantt.show_popup({\n            target_element: this.$bar,\n            title: this.task.name,\n            subtitle: subtitle,\n            task: this.task,\n        });\n    }\n\n    update_bar_position({ x = null, width = null }) {\n        const bar = this.$bar;\n        if (x) {\n            // get all x values of parent task\n            const xs = this.task.dependencies.map((dep) => {\n                return this.gantt.get_bar(dep).$bar.getX();\n            });\n            // child task must not go before parent\n            const valid_x = xs.reduce((prev, curr) => {\n                return x >= curr;\n            }, x);\n            if (!valid_x) {\n                width = null;\n                return;\n            }\n            this.update_attr(bar, 'x', x);\n        }\n        if (width && width >= this.gantt.options.column_width) {\n            this.update_attr(bar, 'width', width);\n        }\n        this.update_label_position();\n        this.update_handle_position();\n        this.update_progressbar_position();\n        this.update_arrow_position();\n    }\n\n    date_changed() {\n        let changed = false;\n        const { new_start_date, new_end_date } = this.compute_start_end_date();\n\n        if (Number(this.task._start) !== Number(new_start_date)) {\n            changed = true;\n            this.task._start = new_start_date;\n        }\n\n        if (Number(this.task._end) !== Number(new_end_date)) {\n            changed = true;\n            this.task._end = new_end_date;\n        }\n\n        if (!changed) return;\n\n        this.gantt.trigger_event('date_change', [\n            this.task,\n            new_start_date,\n            date_utils.add(new_end_date, -1, 'second'),\n        ]);\n    }\n\n    progress_changed() {\n        const new_progress = this.compute_progress();\n        this.task.progress = new_progress;\n        this.gantt.trigger_event('progress_change', [this.task, new_progress]);\n    }\n\n    set_action_completed() {\n        this.action_completed = true;\n        setTimeout(() => (this.action_completed = false), 1000);\n    }\n\n    compute_start_end_date() {\n        const bar = this.$bar;\n        const x_in_units = bar.getX() / this.gantt.options.column_width;\n        const new_start_date = date_utils.add(\n            this.gantt.gantt_start,\n            x_in_units * this.gantt.options.step,\n            'hour'\n        );\n        const width_in_units = bar.getWidth() / this.gantt.options.column_width;\n        const new_end_date = date_utils.add(\n            new_start_date,\n            width_in_units * this.gantt.options.step,\n            'hour'\n        );\n\n        return { new_start_date, new_end_date };\n    }\n\n    compute_progress() {\n        const progress =\n            (this.$bar_progress.getWidth() / this.$bar.getWidth()) * 100;\n        return parseInt(progress, 10);\n    }\n\n    compute_x() {\n        const { step, column_width } = this.gantt.options;\n        const task_start = this.task._start;\n        const gantt_start = this.gantt.gantt_start;\n\n        const diff = date_utils.diff(task_start, gantt_start, 'hour');\n        let x = (diff / step) * column_width;\n\n        if (this.gantt.view_is('Month')) {\n            const diff = date_utils.diff(task_start, gantt_start, 'day');\n            x = (diff * column_width) / 30;\n        }\n        return x;\n    }\n\n    compute_y() {\n        return (\n            this.gantt.options.header_height +\n            this.gantt.options.padding +\n            this.task._index * (this.height + this.gantt.options.padding)\n        );\n    }\n\n    get_snap_position(dx) {\n        let odx = dx,\n            rem,\n            position;\n\n        if (this.gantt.view_is('Week')) {\n            rem = dx % (this.gantt.options.column_width / 7);\n            position =\n                odx -\n                rem +\n                (rem < this.gantt.options.column_width / 14\n                    ? 0\n                    : this.gantt.options.column_width / 7);\n        } else if (this.gantt.view_is('Month')) {\n            rem = dx % (this.gantt.options.column_width / 30);\n            position =\n                odx -\n                rem +\n                (rem < this.gantt.options.column_width / 60\n                    ? 0\n                    : this.gantt.options.column_width / 30);\n        } else {\n            rem = dx % this.gantt.options.column_width;\n            position =\n                odx -\n                rem +\n                (rem < this.gantt.options.column_width / 2\n                    ? 0\n                    : this.gantt.options.column_width);\n        }\n        return position;\n    }\n\n    update_attr(element, attr, value) {\n        value = +value;\n        if (!isNaN(value)) {\n            element.setAttribute(attr, value);\n        }\n        return element;\n    }\n\n    update_progressbar_position() {\n        this.$bar_progress.setAttribute('x', this.$bar.getX());\n        this.$bar_progress.setAttribute(\n            'width',\n            this.$bar.getWidth() * (this.task.progress / 100)\n        );\n    }\n\n    update_label_position() {\n        const bar = this.$bar,\n            label = this.group.querySelector('.bar-label');\n\n        if (label.getBBox().width > bar.getWidth()) {\n            label.classList.add('big');\n            label.setAttribute('x', bar.getX() + bar.getWidth() + 5);\n        } else {\n            label.classList.remove('big');\n            label.setAttribute('x', bar.getX() + bar.getWidth() / 2);\n        }\n    }\n\n    update_handle_position() {\n        const bar = this.$bar;\n        this.handle_group\n            .querySelector('.handle.left')\n            .setAttribute('x', bar.getX() + 1);\n        this.handle_group\n            .querySelector('.handle.right')\n            .setAttribute('x', bar.getEndX() - 9);\n        const handle = this.group.querySelector('.handle.progress');\n        handle &&\n            handle.setAttribute('points', this.get_progress_polygon_points());\n    }\n\n    update_arrow_position() {\n        this.arrows = this.arrows || [];\n        for (let arrow of this.arrows) {\n            arrow.update();\n        }\n    }\n}\n\nfunction isFunction(functionToCheck) {\n    var getType = {};\n    return (\n        functionToCheck &&\n        getType.toString.call(functionToCheck) === '[object Function]'\n    );\n}\n", "import { createSVG } from './svg_utils';\n\nexport default class Arrow {\n    constructor(gantt, from_task, to_task) {\n        this.gantt = gantt;\n        this.from_task = from_task;\n        this.to_task = to_task;\n\n        this.calculate_path();\n        this.draw();\n    }\n\n    calculate_path() {\n        let start_x =\n            this.from_task.$bar.getX() + this.from_task.$bar.getWidth() / 2;\n\n        const condition = () =>\n            this.to_task.$bar.getX() < start_x + this.gantt.options.padding &&\n            start_x > this.from_task.$bar.getX() + this.gantt.options.padding;\n\n        while (condition()) {\n            start_x -= 10;\n        }\n\n        const start_y =\n            this.gantt.options.header_height +\n            this.gantt.options.bar_height +\n            (this.gantt.options.padding + this.gantt.options.bar_height) *\n                this.from_task.task._index +\n            this.gantt.options.padding;\n\n        const end_x = this.to_task.$bar.getX() - this.gantt.options.padding / 2;\n        const end_y =\n            this.gantt.options.header_height +\n            this.gantt.options.bar_height / 2 +\n            (this.gantt.options.padding + this.gantt.options.bar_height) *\n                this.to_task.task._index +\n            this.gantt.options.padding;\n\n        const from_is_below_to =\n            this.from_task.task._index > this.to_task.task._index;\n        const curve = this.gantt.options.arrow_curve;\n        const clockwise = from_is_below_to ? 1 : 0;\n        const curve_y = from_is_below_to ? -curve : curve;\n        const offset = from_is_below_to\n            ? end_y + this.gantt.options.arrow_curve\n            : end_y - this.gantt.options.arrow_curve;\n\n        this.path = `\n            M ${start_x} ${start_y}\n            V ${offset}\n            a ${curve} ${curve} 0 0 ${clockwise} ${curve} ${curve_y}\n            L ${end_x} ${end_y}\n            m -5 -5\n            l 5 5\n            l -5 5`;\n\n        if (\n            this.to_task.$bar.getX() <\n            this.from_task.$bar.getX() + this.gantt.options.padding\n        ) {\n            const down_1 = this.gantt.options.padding / 2 - curve;\n            const down_2 =\n                this.to_task.$bar.getY() +\n                this.to_task.$bar.getHeight() / 2 -\n                curve_y;\n            const left = this.to_task.$bar.getX() - this.gantt.options.padding;\n\n            this.path = `\n                M ${start_x} ${start_y}\n                v ${down_1}\n                a ${curve} ${curve} 0 0 1 -${curve} ${curve}\n                H ${left}\n                a ${curve} ${curve} 0 0 ${clockwise} -${curve} ${curve_y}\n                V ${down_2}\n                a ${curve} ${curve} 0 0 ${clockwise} ${curve} ${curve_y}\n                L ${end_x} ${end_y}\n                m -5 -5\n                l 5 5\n                l -5 5`;\n        }\n    }\n\n    draw() {\n        this.element = createSVG('path', {\n            d: this.path,\n            'data-from': this.from_task.task.id,\n            'data-to': this.to_task.task.id,\n        });\n    }\n\n    update() {\n        this.calculate_path();\n        this.element.setAttribute('d', this.path);\n    }\n}\n", "export default class Popup {\n    constructor(parent, custom_html) {\n        this.parent = parent;\n        this.custom_html = custom_html;\n        this.make();\n    }\n\n    make() {\n        this.parent.innerHTML = `\n            <div class=\"title\"></div>\n            <div class=\"subtitle\"></div>\n            <div class=\"pointer\"></div>\n        `;\n\n        this.hide();\n\n        this.title = this.parent.querySelector('.title');\n        this.subtitle = this.parent.querySelector('.subtitle');\n        this.pointer = this.parent.querySelector('.pointer');\n    }\n\n    show(options) {\n        if (!options.target_element) {\n            throw new Error('target_element is required to show popup');\n        }\n        if (!options.position) {\n            options.position = 'left';\n        }\n        const target_element = options.target_element;\n\n        if (this.custom_html) {\n            let html = this.custom_html(options.task);\n            html += '<div class=\"pointer\"></div>';\n            this.parent.innerHTML = html;\n            this.pointer = this.parent.querySelector('.pointer');\n        } else {\n            // set data\n            this.title.innerHTML = options.title;\n            this.subtitle.innerHTML = options.subtitle;\n            this.parent.style.width = this.parent.clientWidth + 'px';\n        }\n\n        // set position\n        let position_meta;\n        if (target_element instanceof HTMLElement) {\n            position_meta = target_element.getBoundingClientRect();\n        } else if (target_element instanceof SVGElement) {\n            position_meta = options.target_element.getBBox();\n        }\n\n        if (options.position === 'left') {\n            this.parent.style.left =\n                position_meta.x + (position_meta.width + 10) + 'px';\n            this.parent.style.top = position_meta.y + 'px';\n\n            this.pointer.style.transform = 'rotateZ(90deg)';\n            this.pointer.style.left = '-7px';\n            this.pointer.style.top = '2px';\n        }\n\n        // show\n        this.parent.style.opacity = 1;\n    }\n\n    hide() {\n        this.parent.style.opacity = 0;\n        this.parent.style.left = 0;\n    }\n}\n", "import date_utils from './date_utils';\nimport { $, createSVG } from './svg_utils';\nimport Bar from './bar';\nimport Arrow from './arrow';\nimport Popup from './popup';\n\nimport './gantt.scss';\n\nconst VIEW_MODE = {\n    QUARTER_DAY: 'Quarter Day',\n    HALF_DAY: 'Half Day',\n    DAY: 'Day',\n    WEEK: 'Week',\n    MONTH: 'Month',\n    YEAR: 'Year',\n};\n\nexport default class Gantt {\n    constructor(wrapper, tasks, options) {\n        this.setup_wrapper(wrapper);\n        this.setup_options(options);\n        this.setup_tasks(tasks);\n        // initialize with default view mode\n        this.change_view_mode();\n        this.bind_events();\n    }\n\n    setup_wrapper(element) {\n        let svg_element, wrapper_element;\n\n        // CSS Selector is passed\n        if (typeof element === 'string') {\n            element = document.querySelector(element);\n        }\n\n        // get the SVGElement\n        if (element instanceof HTMLElement) {\n            wrapper_element = element;\n            svg_element = element.querySelector('svg');\n        } else if (element instanceof SVGElement) {\n            svg_element = element;\n        } else {\n            throw new TypeError(\n                'Frappé Gantt only supports usage of a string CSS selector,' +\n                    \" HTML DOM element or SVG DOM element for the 'element' parameter\"\n            );\n        }\n\n        // svg element\n        if (!svg_element) {\n            // create it\n            this.$svg = createSVG('svg', {\n                append_to: wrapper_element,\n                class: 'gantt',\n            });\n        } else {\n            this.$svg = svg_element;\n            this.$svg.classList.add('gantt');\n        }\n\n        // wrapper element\n        this.$container = document.createElement('div');\n        this.$container.classList.add('gantt-container');\n\n        const parent_element = this.$svg.parentElement;\n        parent_element.appendChild(this.$container);\n        this.$container.appendChild(this.$svg);\n\n        // popup wrapper\n        this.popup_wrapper = document.createElement('div');\n        this.popup_wrapper.classList.add('popup-wrapper');\n        this.$container.appendChild(this.popup_wrapper);\n    }\n\n    setup_options(options) {\n        const default_options = {\n            header_height: 50,\n            column_width: 30,\n            step: 24,\n            view_modes: [...Object.values(VIEW_MODE)],\n            bar_height: 20,\n            bar_corner_radius: 3,\n            arrow_curve: 5,\n            padding: 18,\n            view_mode: 'Day',\n            date_format: 'YYYY-MM-DD',\n            popup_trigger: 'click',\n            custom_popup_html: null,\n            language: 'en',\n        };\n        this.options = Object.assign({}, default_options, options);\n    }\n\n    setup_tasks(tasks) {\n        // prepare tasks\n        this.tasks = tasks.map((task, i) => {\n            // convert to Date objects\n            task._start = date_utils.parse(task.start);\n            task._end = date_utils.parse(task.end);\n\n            // make task invalid if duration too large\n            if (date_utils.diff(task._end, task._start, 'year') > 10) {\n                task.end = null;\n            }\n\n            // cache index\n            task._index = i;\n\n            // invalid dates\n            if (!task.start && !task.end) {\n                const today = date_utils.today();\n                task._start = today;\n                task._end = date_utils.add(today, 2, 'day');\n            }\n\n            if (!task.start && task.end) {\n                task._start = date_utils.add(task._end, -2, 'day');\n            }\n\n            if (task.start && !task.end) {\n                task._end = date_utils.add(task._start, 2, 'day');\n            }\n\n            // if hours is not set, assume the last day is full day\n            // e.g: 2018-09-09 becomes 2018-09-09 23:59:59\n            const task_end_values = date_utils.get_date_values(task._end);\n            if (task_end_values.slice(3).every((d) => d === 0)) {\n                task._end = date_utils.add(task._end, 24, 'hour');\n            }\n\n            // invalid flag\n            if (!task.start || !task.end) {\n                task.invalid = true;\n            }\n\n            // dependencies\n            if (typeof task.dependencies === 'string' || !task.dependencies) {\n                let deps = [];\n                if (task.dependencies) {\n                    deps = task.dependencies\n                        .split(',')\n                        .map((d) => d.trim())\n                        .filter((d) => d);\n                }\n                task.dependencies = deps;\n            }\n\n            // uids\n            if (!task.id) {\n                task.id = generate_id(task);\n            }\n\n            return task;\n        });\n\n        this.setup_dependencies();\n    }\n\n    setup_dependencies() {\n        this.dependency_map = {};\n        for (let t of this.tasks) {\n            for (let d of t.dependencies) {\n                this.dependency_map[d] = this.dependency_map[d] || [];\n                this.dependency_map[d].push(t.id);\n            }\n        }\n    }\n\n    refresh(tasks) {\n        this.setup_tasks(tasks);\n        this.change_view_mode();\n    }\n\n    change_view_mode(mode = this.options.view_mode) {\n        this.update_view_scale(mode);\n        this.setup_dates();\n        this.render();\n        // fire viewmode_change event\n        this.trigger_event('view_change', [mode]);\n    }\n\n    update_view_scale(view_mode) {\n        this.options.view_mode = view_mode;\n\n        if (view_mode === VIEW_MODE.DAY) {\n            this.options.step = 24;\n            this.options.column_width = 38;\n        } else if (view_mode === VIEW_MODE.HALF_DAY) {\n            this.options.step = 24 / 2;\n            this.options.column_width = 38;\n        } else if (view_mode === VIEW_MODE.QUARTER_DAY) {\n            this.options.step = 24 / 4;\n            this.options.column_width = 38;\n        } else if (view_mode === VIEW_MODE.WEEK) {\n            this.options.step = 24 * 7;\n            this.options.column_width = 140;\n        } else if (view_mode === VIEW_MODE.MONTH) {\n            this.options.step = 24 * 30;\n            this.options.column_width = 120;\n        } else if (view_mode === VIEW_MODE.YEAR) {\n            this.options.step = 24 * 365;\n            this.options.column_width = 120;\n        }\n    }\n\n    setup_dates() {\n        this.setup_gantt_dates();\n        this.setup_date_values();\n    }\n\n    setup_gantt_dates() {\n        this.gantt_start = this.gantt_end = null;\n\n        for (let task of this.tasks) {\n            // set global start and end date\n            if (!this.gantt_start || task._start < this.gantt_start) {\n                this.gantt_start = task._start;\n            }\n            if (!this.gantt_end || task._end > this.gantt_end) {\n                this.gantt_end = task._end;\n            }\n        }\n\n        this.gantt_start = date_utils.start_of(this.gantt_start, 'day');\n        this.gantt_end = date_utils.start_of(this.gantt_end, 'day');\n\n        // add date padding on both sides\n        if (this.view_is([VIEW_MODE.QUARTER_DAY, VIEW_MODE.HALF_DAY])) {\n            this.gantt_start = date_utils.add(this.gantt_start, -7, 'day');\n            this.gantt_end = date_utils.add(this.gantt_end, 7, 'day');\n        } else if (this.view_is(VIEW_MODE.MONTH)) {\n            this.gantt_start = date_utils.start_of(this.gantt_start, 'year');\n            this.gantt_end = date_utils.add(this.gantt_end, 1, 'year');\n        } else if (this.view_is(VIEW_MODE.YEAR)) {\n            this.gantt_start = date_utils.add(this.gantt_start, -2, 'year');\n            this.gantt_end = date_utils.add(this.gantt_end, 2, 'year');\n        } else {\n            this.gantt_start = date_utils.add(this.gantt_start, -1, 'month');\n            this.gantt_end = date_utils.add(this.gantt_end, 1, 'month');\n        }\n    }\n\n    setup_date_values() {\n        this.dates = [];\n        let cur_date = null;\n\n        while (cur_date === null || cur_date < this.gantt_end) {\n            if (!cur_date) {\n                cur_date = date_utils.clone(this.gantt_start);\n            } else {\n                if (this.view_is(VIEW_MODE.YEAR)) {\n                    cur_date = date_utils.add(cur_date, 1, 'year');\n                } else if (this.view_is(VIEW_MODE.MONTH)) {\n                    cur_date = date_utils.add(cur_date, 1, 'month');\n                } else {\n                    cur_date = date_utils.add(\n                        cur_date,\n                        this.options.step,\n                        'hour'\n                    );\n                }\n            }\n            this.dates.push(cur_date);\n        }\n    }\n\n    bind_events() {\n        this.bind_grid_click();\n        this.bind_bar_events();\n    }\n\n    render() {\n        this.clear();\n        this.setup_layers();\n        this.make_grid();\n        this.make_dates();\n        this.make_bars();\n        this.make_arrows();\n        this.map_arrows_on_bars();\n        this.set_width();\n        this.set_scroll_position();\n    }\n\n    setup_layers() {\n        this.layers = {};\n        const layers = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n        // make group layers\n        for (let layer of layers) {\n            this.layers[layer] = createSVG('g', {\n                class: layer,\n                append_to: this.$svg,\n            });\n        }\n    }\n\n    make_grid() {\n        this.make_grid_background();\n        this.make_grid_rows();\n        this.make_grid_header();\n        this.make_grid_ticks();\n        this.make_grid_highlights();\n    }\n\n    make_grid_background() {\n        const grid_width = this.dates.length * this.options.column_width;\n        const grid_height =\n            this.options.header_height +\n            this.options.padding +\n            (this.options.bar_height + this.options.padding) *\n                this.tasks.length;\n\n        createSVG('rect', {\n            x: 0,\n            y: 0,\n            width: grid_width,\n            height: grid_height,\n            class: 'grid-background',\n            append_to: this.layers.grid,\n        });\n\n        $.attr(this.$svg, {\n            height: grid_height + this.options.padding + 100,\n            width: '100%',\n        });\n    }\n\n    make_grid_rows() {\n        const rows_layer = createSVG('g', { append_to: this.layers.grid });\n        const lines_layer = createSVG('g', { append_to: this.layers.grid });\n\n        const row_width = this.dates.length * this.options.column_width;\n        const row_height = this.options.bar_height + this.options.padding;\n\n        let row_y = this.options.header_height + this.options.padding / 2;\n\n        for (let task of this.tasks) {\n            createSVG('rect', {\n                x: 0,\n                y: row_y,\n                width: row_width,\n                height: row_height,\n                class: 'grid-row',\n                append_to: rows_layer,\n            });\n\n            createSVG('line', {\n                x1: 0,\n                y1: row_y + row_height,\n                x2: row_width,\n                y2: row_y + row_height,\n                class: 'row-line',\n                append_to: lines_layer,\n            });\n\n            row_y += this.options.bar_height + this.options.padding;\n        }\n    }\n\n    make_grid_header() {\n        const header_width = this.dates.length * this.options.column_width;\n        const header_height = this.options.header_height + 10;\n        createSVG('rect', {\n            x: 0,\n            y: 0,\n            width: header_width,\n            height: header_height,\n            class: 'grid-header',\n            append_to: this.layers.grid,\n        });\n    }\n\n    make_grid_ticks() {\n        let tick_x = 0;\n        let tick_y = this.options.header_height + this.options.padding / 2;\n        let tick_height =\n            (this.options.bar_height + this.options.padding) *\n            this.tasks.length;\n\n        for (let date of this.dates) {\n            let tick_class = 'tick';\n            // thick tick for monday\n            if (this.view_is(VIEW_MODE.DAY) && date.getDate() === 1) {\n                tick_class += ' thick';\n            }\n            // thick tick for first week\n            if (\n                this.view_is(VIEW_MODE.WEEK) &&\n                date.getDate() >= 1 &&\n                date.getDate() < 8\n            ) {\n                tick_class += ' thick';\n            }\n            // thick ticks for quarters\n            if (\n                this.view_is(VIEW_MODE.MONTH) &&\n                (date.getMonth() + 1) % 3 === 0\n            ) {\n                tick_class += ' thick';\n            }\n\n            createSVG('path', {\n                d: `M ${tick_x} ${tick_y} v ${tick_height}`,\n                class: tick_class,\n                append_to: this.layers.grid,\n            });\n\n            if (this.view_is(VIEW_MODE.MONTH)) {\n                tick_x +=\n                    (date_utils.get_days_in_month(date) *\n                        this.options.column_width) /\n                    30;\n            } else {\n                tick_x += this.options.column_width;\n            }\n        }\n    }\n\n    make_grid_highlights() {\n        // highlight today's date\n        if (this.view_is(VIEW_MODE.DAY)) {\n            const x =\n                (date_utils.diff(date_utils.today(), this.gantt_start, 'hour') /\n                    this.options.step) *\n                this.options.column_width;\n            const y = 0;\n\n            const width = this.options.column_width;\n            const height =\n                (this.options.bar_height + this.options.padding) *\n                    this.tasks.length +\n                this.options.header_height +\n                this.options.padding / 2;\n\n            createSVG('rect', {\n                x,\n                y,\n                width,\n                height,\n                class: 'today-highlight',\n                append_to: this.layers.grid,\n            });\n        }\n    }\n\n    make_dates() {\n        for (let date of this.get_dates_to_draw()) {\n            createSVG('text', {\n                x: date.lower_x,\n                y: date.lower_y,\n                innerHTML: date.lower_text,\n                class: 'lower-text',\n                append_to: this.layers.date,\n            });\n\n            if (date.upper_text) {\n                const $upper_text = createSVG('text', {\n                    x: date.upper_x,\n                    y: date.upper_y,\n                    innerHTML: date.upper_text,\n                    class: 'upper-text',\n                    append_to: this.layers.date,\n                });\n\n                // remove out-of-bound dates\n                if (\n                    $upper_text.getBBox().x2 > this.layers.grid.getBBox().width\n                ) {\n                    $upper_text.remove();\n                }\n            }\n        }\n    }\n\n    get_dates_to_draw() {\n        let last_date = null;\n        const dates = this.dates.map((date, i) => {\n            const d = this.get_date_info(date, last_date, i);\n            last_date = date;\n            return d;\n        });\n        return dates;\n    }\n\n    get_date_info(date, last_date, i) {\n        if (!last_date) {\n            last_date = date_utils.add(date, 1, 'year');\n        }\n        const date_text = {\n            'Quarter Day_lower': date_utils.format(\n                date,\n                'HH',\n                this.options.language\n            ),\n            'Half Day_lower': date_utils.format(\n                date,\n                'HH',\n                this.options.language\n            ),\n            Day_lower:\n                date.getDate() !== last_date.getDate()\n                    ? date_utils.format(date, 'D', this.options.language)\n                    : '',\n            Week_lower:\n                date.getMonth() !== last_date.getMonth()\n                    ? date_utils.format(date, 'D MMM', this.options.language)\n                    : date_utils.format(date, 'D', this.options.language),\n            Month_lower: date_utils.format(date, 'MMMM', this.options.language),\n            Year_lower: date_utils.format(date, 'YYYY', this.options.language),\n            'Quarter Day_upper':\n                date.getDate() !== last_date.getDate()\n                    ? date_utils.format(date, 'D MMM', this.options.language)\n                    : '',\n            'Half Day_upper':\n                date.getDate() !== last_date.getDate()\n                    ? date.getMonth() !== last_date.getMonth()\n                        ? date_utils.format(\n                              date,\n                              'D MMM',\n                              this.options.language\n                          )\n                        : date_utils.format(date, 'D', this.options.language)\n                    : '',\n            Day_upper:\n                date.getMonth() !== last_date.getMonth()\n                    ? date_utils.format(date, 'MMMM', this.options.language)\n                    : '',\n            Week_upper:\n                date.getMonth() !== last_date.getMonth()\n                    ? date_utils.format(date, 'MMMM', this.options.language)\n                    : '',\n            Month_upper:\n                date.getFullYear() !== last_date.getFullYear()\n                    ? date_utils.format(date, 'YYYY', this.options.language)\n                    : '',\n            Year_upper:\n                date.getFullYear() !== last_date.getFullYear()\n                    ? date_utils.format(date, 'YYYY', this.options.language)\n                    : '',\n        };\n\n        const base_pos = {\n            x: i * this.options.column_width,\n            lower_y: this.options.header_height,\n            upper_y: this.options.header_height - 25,\n        };\n\n        const x_pos = {\n            'Quarter Day_lower': (this.options.column_width * 4) / 2,\n            'Quarter Day_upper': 0,\n            'Half Day_lower': (this.options.column_width * 2) / 2,\n            'Half Day_upper': 0,\n            Day_lower: this.options.column_width / 2,\n            Day_upper: (this.options.column_width * 30) / 2,\n            Week_lower: 0,\n            Week_upper: (this.options.column_width * 4) / 2,\n            Month_lower: this.options.column_width / 2,\n            Month_upper: (this.options.column_width * 12) / 2,\n            Year_lower: this.options.column_width / 2,\n            Year_upper: (this.options.column_width * 30) / 2,\n        };\n\n        return {\n            upper_text: date_text[`${this.options.view_mode}_upper`],\n            lower_text: date_text[`${this.options.view_mode}_lower`],\n            upper_x: base_pos.x + x_pos[`${this.options.view_mode}_upper`],\n            upper_y: base_pos.upper_y,\n            lower_x: base_pos.x + x_pos[`${this.options.view_mode}_lower`],\n            lower_y: base_pos.lower_y,\n        };\n    }\n\n    make_bars() {\n        this.bars = this.tasks.map((task) => {\n            const bar = new Bar(this, task);\n            this.layers.bar.appendChild(bar.group);\n            return bar;\n        });\n    }\n\n    make_arrows() {\n        this.arrows = [];\n        for (let task of this.tasks) {\n            let arrows = [];\n            arrows = task.dependencies\n                .map((task_id) => {\n                    const dependency = this.get_task(task_id);\n                    if (!dependency) return;\n                    const arrow = new Arrow(\n                        this,\n                        this.bars[dependency._index], // from_task\n                        this.bars[task._index] // to_task\n                    );\n                    this.layers.arrow.appendChild(arrow.element);\n                    return arrow;\n                })\n                .filter(Boolean); // filter falsy values\n            this.arrows = this.arrows.concat(arrows);\n        }\n    }\n\n    map_arrows_on_bars() {\n        for (let bar of this.bars) {\n            bar.arrows = this.arrows.filter((arrow) => {\n                return (\n                    arrow.from_task.task.id === bar.task.id ||\n                    arrow.to_task.task.id === bar.task.id\n                );\n            });\n        }\n    }\n\n    set_width() {\n        const cur_width = this.$svg.getBoundingClientRect().width;\n        const actual_width = this.$svg\n            .querySelector('.grid .grid-row')\n            .getAttribute('width');\n        if (cur_width < actual_width) {\n            this.$svg.setAttribute('width', actual_width);\n        }\n    }\n\n    set_scroll_position() {\n        const parent_element = this.$svg.parentElement;\n        if (!parent_element) return;\n\n        const hours_before_first_task = date_utils.diff(\n            this.get_oldest_starting_date(),\n            this.gantt_start,\n            'hour'\n        );\n\n        const scroll_pos =\n            (hours_before_first_task / this.options.step) *\n                this.options.column_width -\n            this.options.column_width;\n\n        parent_element.scrollLeft = scroll_pos;\n    }\n\n    bind_grid_click() {\n        $.on(\n            this.$svg,\n            this.options.popup_trigger,\n            '.grid-row, .grid-header',\n            () => {\n                this.unselect_all();\n                this.hide_popup();\n            }\n        );\n    }\n\n    bind_bar_events() {\n        let is_dragging = false;\n        let x_on_start = 0;\n        let y_on_start = 0;\n        let is_resizing_left = false;\n        let is_resizing_right = false;\n        let parent_bar_id = null;\n        let bars = []; // instanceof Bar\n        this.bar_being_dragged = null;\n\n        function action_in_progress() {\n            return is_dragging || is_resizing_left || is_resizing_right;\n        }\n\n        $.on(this.$svg, 'mousedown', '.bar-wrapper, .handle', (e, element) => {\n            const bar_wrapper = $.closest('.bar-wrapper', element);\n\n            if (element.classList.contains('left')) {\n                is_resizing_left = true;\n            } else if (element.classList.contains('right')) {\n                is_resizing_right = true;\n            } else if (element.classList.contains('bar-wrapper')) {\n                is_dragging = true;\n            }\n\n            bar_wrapper.classList.add('active');\n\n            x_on_start = e.offsetX;\n            y_on_start = e.offsetY;\n\n            parent_bar_id = bar_wrapper.getAttribute('data-id');\n            const ids = [\n                parent_bar_id,\n                ...this.get_all_dependent_tasks(parent_bar_id),\n            ];\n            bars = ids.map((id) => this.get_bar(id));\n\n            this.bar_being_dragged = parent_bar_id;\n\n            bars.forEach((bar) => {\n                const $bar = bar.$bar;\n                $bar.ox = $bar.getX();\n                $bar.oy = $bar.getY();\n                $bar.owidth = $bar.getWidth();\n                $bar.finaldx = 0;\n            });\n        });\n\n        $.on(this.$svg, 'mousemove', (e) => {\n            if (!action_in_progress()) return;\n            const dx = e.offsetX - x_on_start;\n            const dy = e.offsetY - y_on_start;\n\n            bars.forEach((bar) => {\n                const $bar = bar.$bar;\n                $bar.finaldx = this.get_snap_position(dx);\n                this.hide_popup();\n                if (is_resizing_left) {\n                    if (parent_bar_id === bar.task.id) {\n                        bar.update_bar_position({\n                            x: $bar.ox + $bar.finaldx,\n                            width: $bar.owidth - $bar.finaldx,\n                        });\n                    } else {\n                        bar.update_bar_position({\n                            x: $bar.ox + $bar.finaldx,\n                        });\n                    }\n                } else if (is_resizing_right) {\n                    if (parent_bar_id === bar.task.id) {\n                        bar.update_bar_position({\n                            width: $bar.owidth + $bar.finaldx,\n                        });\n                    }\n                } else if (is_dragging) {\n                    bar.update_bar_position({ x: $bar.ox + $bar.finaldx });\n                }\n            });\n        });\n\n        document.addEventListener('mouseup', (e) => {\n            if (is_dragging || is_resizing_left || is_resizing_right) {\n                bars.forEach((bar) => bar.group.classList.remove('active'));\n            }\n\n            is_dragging = false;\n            is_resizing_left = false;\n            is_resizing_right = false;\n        });\n\n        $.on(this.$svg, 'mouseup', (e) => {\n            this.bar_being_dragged = null;\n            bars.forEach((bar) => {\n                const $bar = bar.$bar;\n                if (!$bar.finaldx) return;\n                bar.date_changed();\n                bar.set_action_completed();\n            });\n        });\n\n        this.bind_bar_progress();\n    }\n\n    bind_bar_progress() {\n        let x_on_start = 0;\n        let y_on_start = 0;\n        let is_resizing = null;\n        let bar = null;\n        let $bar_progress = null;\n        let $bar = null;\n\n        $.on(this.$svg, 'mousedown', '.handle.progress', (e, handle) => {\n            is_resizing = true;\n            x_on_start = e.offsetX;\n            y_on_start = e.offsetY;\n\n            const $bar_wrapper = $.closest('.bar-wrapper', handle);\n            const id = $bar_wrapper.getAttribute('data-id');\n            bar = this.get_bar(id);\n\n            $bar_progress = bar.$bar_progress;\n            $bar = bar.$bar;\n\n            $bar_progress.finaldx = 0;\n            $bar_progress.owidth = $bar_progress.getWidth();\n            $bar_progress.min_dx = -$bar_progress.getWidth();\n            $bar_progress.max_dx = $bar.getWidth() - $bar_progress.getWidth();\n        });\n\n        $.on(this.$svg, 'mousemove', (e) => {\n            if (!is_resizing) return;\n            let dx = e.offsetX - x_on_start;\n            let dy = e.offsetY - y_on_start;\n\n            if (dx > $bar_progress.max_dx) {\n                dx = $bar_progress.max_dx;\n            }\n            if (dx < $bar_progress.min_dx) {\n                dx = $bar_progress.min_dx;\n            }\n\n            const $handle = bar.$handle_progress;\n            $.attr($bar_progress, 'width', $bar_progress.owidth + dx);\n            $.attr($handle, 'points', bar.get_progress_polygon_points());\n            $bar_progress.finaldx = dx;\n        });\n\n        $.on(this.$svg, 'mouseup', () => {\n            is_resizing = false;\n            if (!($bar_progress && $bar_progress.finaldx)) return;\n            bar.progress_changed();\n            bar.set_action_completed();\n        });\n    }\n\n    get_all_dependent_tasks(task_id) {\n        let out = [];\n        let to_process = [task_id];\n        while (to_process.length) {\n            const deps = to_process.reduce((acc, curr) => {\n                acc = acc.concat(this.dependency_map[curr]);\n                return acc;\n            }, []);\n\n            out = out.concat(deps);\n            to_process = deps.filter((d) => !to_process.includes(d));\n        }\n\n        return out.filter(Boolean);\n    }\n\n    get_snap_position(dx) {\n        let odx = dx,\n            rem,\n            position;\n\n        if (this.view_is(VIEW_MODE.WEEK)) {\n            rem = dx % (this.options.column_width / 7);\n            position =\n                odx -\n                rem +\n                (rem < this.options.column_width / 14\n                    ? 0\n                    : this.options.column_width / 7);\n        } else if (this.view_is(VIEW_MODE.MONTH)) {\n            rem = dx % (this.options.column_width / 30);\n            position =\n                odx -\n                rem +\n                (rem < this.options.column_width / 60\n                    ? 0\n                    : this.options.column_width / 30);\n        } else {\n            rem = dx % this.options.column_width;\n            position =\n                odx -\n                rem +\n                (rem < this.options.column_width / 2\n                    ? 0\n                    : this.options.column_width);\n        }\n        return position;\n    }\n\n    unselect_all() {\n        [...this.$svg.querySelectorAll('.bar-wrapper')].forEach((el) => {\n            el.classList.remove('active');\n        });\n    }\n\n    view_is(modes) {\n        if (typeof modes === 'string') {\n            return this.options.view_mode === modes;\n        }\n\n        if (Array.isArray(modes)) {\n            return modes.some((mode) => this.options.view_mode === mode);\n        }\n\n        return false;\n    }\n\n    get_task(id) {\n        return this.tasks.find((task) => {\n            return task.id === id;\n        });\n    }\n\n    get_bar(id) {\n        return this.bars.find((bar) => {\n            return bar.task.id === id;\n        });\n    }\n\n    show_popup(options) {\n        if (!this.popup) {\n            this.popup = new Popup(\n                this.popup_wrapper,\n                this.options.custom_popup_html\n            );\n        }\n        this.popup.show(options);\n    }\n\n    hide_popup() {\n        this.popup && this.popup.hide();\n    }\n\n    trigger_event(event, args) {\n        if (this.options['on_' + event]) {\n            this.options['on_' + event].apply(null, args);\n        }\n    }\n\n    /**\n     * Gets the oldest starting date from the list of tasks\n     *\n     * @returns Date\n     * @memberof Gantt\n     */\n    get_oldest_starting_date() {\n        return this.tasks\n            .map((task) => task._start)\n            .reduce((prev_date, cur_date) =>\n                cur_date <= prev_date ? cur_date : prev_date\n            );\n    }\n\n    /**\n     * Clear all elements from the parent svg element\n     *\n     * @memberof Gantt\n     */\n    clear() {\n        this.$svg.innerHTML = '';\n    }\n}\n\nGantt.VIEW_MODE = VIEW_MODE;\n\nfunction generate_id(task) {\n    return task.name + '_' + Math.random().toString(36).slice(2, 12);\n}\n"], "names": [], "mappings": ";;;IAAA,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,KAAK,GAAG,OAAO,CAAC;IACtB,MAAM,GAAG,GAAG,KAAK,CAAC;IAClB,MAAM,IAAI,GAAG,MAAM,CAAC;IACpB,MAAM,MAAM,GAAG,QAAQ,CAAC;IACxB,MAAM,MAAM,GAAG,QAAQ,CAAC;IACxB,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC;IACA,MAAM,WAAW,GAAG;IACpB,IAAI,EAAE,EAAE;IACR,QAAQ,SAAS;IACjB,QAAQ,UAAU;IAClB,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,KAAK;IACb,QAAQ,MAAM;IACd,QAAQ,MAAM;IACd,QAAQ,QAAQ;IAChB,QAAQ,WAAW;IACnB,QAAQ,SAAS;IACjB,QAAQ,UAAU;IAClB,QAAQ,UAAU;IAClB,KAAK;IACL,IAAI,EAAE,EAAE;IACR,QAAQ,OAAO;IACf,QAAQ,SAAS;IACjB,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,QAAQ;IAChB,QAAQ,YAAY;IACpB,QAAQ,SAAS;IACjB,QAAQ,WAAW;IACnB,QAAQ,WAAW;IACnB,KAAK;IACL,IAAI,EAAE,EAAE;IACR,QAAQ,QAAQ;IAChB,QAAQ,SAAS;IACjB,QAAQ,MAAM;IACd,QAAQ,QAAQ;IAChB,QAAQ,KAAK;IACb,QAAQ,MAAM;IACd,QAAQ,MAAM;IACd,QAAQ,QAAQ;IAChB,QAAQ,UAAU;IAClB,QAAQ,SAAS;IACjB,QAAQ,QAAQ;IAChB,QAAQ,SAAS;IACjB,KAAK;IACL,IAAI,IAAI,EAAE;IACV,QAAQ,SAAS;IACjB,QAAQ,WAAW;IACnB,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,QAAQ;IAChB,QAAQ,UAAU;IAClB,QAAQ,SAAS;IACjB,QAAQ,UAAU;IAClB,QAAQ,UAAU;IAClB,KAAK;IACL,IAAI,EAAE,EAAE;IACR,QAAQ,SAAS;IACjB,QAAQ,SAAS;IACjB,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,KAAK;IACb,QAAQ,MAAM;IACd,QAAQ,SAAS;IACjB,QAAQ,MAAM;IACd,QAAQ,WAAW;IACnB,QAAQ,SAAS;IACjB,QAAQ,UAAU;IAClB,QAAQ,UAAU;IAClB,KAAK;IACL,IAAI,EAAE,EAAE;IACR,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,OAAO;IACf,QAAQ,SAAS;IACjB,QAAQ,QAAQ;IAChB,QAAQ,SAAS;IACjB,QAAQ,OAAO;IACf,QAAQ,MAAM;IACd,QAAQ,OAAO;IACf,QAAQ,QAAQ;IAChB,KAAK;IACL,IAAI,EAAE,EAAE;IACR,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,IAAI;IACZ,QAAQ,KAAK;IACb,QAAQ,KAAK;IACb,KAAK;IACL,CAAC,CAAC;AACF;AACA,qBAAe;IACf,IAAI,KAAK,CAAC,IAAI,EAAE,cAAc,GAAG,GAAG,EAAE,cAAc,GAAG,MAAM,EAAE;IAC/D,QAAQ,IAAI,IAAI,YAAY,IAAI,EAAE;IAClC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;IACtC,YAAY,IAAI,UAAU,EAAE,UAAU,CAAC;IACvC,YAAY,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C;IACA,YAAY,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;IACjC,iBAAiB,KAAK,CAAC,cAAc,CAAC;IACtC,iBAAiB,GAAG,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IACjD,YAAY,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACpE;IACA;IACA,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C;IACA,YAAY,IAAI,IAAI,GAAG,UAAU,CAAC;AAClC;IACA,YAAY,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;IACjD,gBAAgB,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;IAC5C,oBAAoB,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACzD,oBAAoB,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACrE,iBAAiB;IACjB,gBAAgB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC/C,aAAa;AACb;IACA,YAAY,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACrC,SAAS;IACT,KAAK;AACL;IACA,IAAI,SAAS,CAAC,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE;IACvC,QAAQ,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC,EAAE;IACrC,YAAY,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACzD,SAAS;IACT,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;IAChE,YAAY,IAAI,CAAC,KAAK,CAAC,EAAE;IACzB;IACA,gBAAgB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IAC9B,aAAa;AACb;IACA,YAAY,IAAI,CAAC,KAAK,CAAC,EAAE;IACzB,gBAAgB,OAAO,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAClD,aAAa;AACb;IACA,YAAY,OAAO,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9C,SAAS,CAAC,CAAC;IACX,QAAQ,MAAM,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,QAAQ,MAAM,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E;IACA,QAAQ,OAAO,WAAW,IAAI,SAAS,GAAG,GAAG,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC;IAClE,KAAK;AACL;IACA,IAAI,MAAM,CAAC,IAAI,EAAE,aAAa,GAAG,yBAAyB,EAAE,IAAI,GAAG,IAAI,EAAE;IACzE,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChF,QAAQ,MAAM,UAAU,GAAG;IAC3B,YAAY,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAC3B,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACzB,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACzB,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACzB,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACzB,YAAY,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1B,YAAY,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACxB,YAAY,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/C,YAAY,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,SAAS,CAAC;AACV;IACA,QAAQ,IAAI,GAAG,GAAG,aAAa,CAAC;IAChC,QAAQ,MAAM,gBAAgB,GAAG,EAAE,CAAC;AACpC;IACA,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;IAC/B,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;IAChD,aAAa,OAAO,CAAC,CAAC,GAAG,KAAK;IAC9B,gBAAgB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvC,oBAAoB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1E,oBAAoB,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3D,iBAAiB;IACjB,aAAa,CAAC,CAAC;AACf;IACA,QAAQ,gBAAgB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK;IAC/C,YAAY,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9C,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,OAAO,GAAG,CAAC;IACnB,KAAK;AACL;IACA,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,GAAG,EAAE;IACtC,QAAQ,IAAI,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC;AACvE;IACA,QAAQ,YAAY,GAAG,MAAM,GAAG,MAAM,CAAC;IACvC,QAAQ,OAAO,GAAG,YAAY,GAAG,IAAI,CAAC;IACtC,QAAQ,OAAO,GAAG,OAAO,GAAG,EAAE,CAAC;IAC/B,QAAQ,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC;IAC7B,QAAQ,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;IAC1B,QAAQ,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;IAC3B,QAAQ,KAAK,GAAG,MAAM,GAAG,EAAE,CAAC;AAC5B;IACA,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClC,YAAY,KAAK,IAAI,GAAG,CAAC;IACzB,SAAS;AACT;IACA,QAAQ,OAAO,IAAI,CAAC,KAAK;IACzB,YAAY;IACZ,gBAAgB,YAAY;IAC5B,gBAAgB,OAAO;IACvB,gBAAgB,OAAO;IACvB,gBAAgB,KAAK;IACrB,gBAAgB,IAAI;IACpB,gBAAgB,MAAM;IACtB,gBAAgB,KAAK;IACrB,aAAa,CAAC,KAAK,CAAC;IACpB,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,KAAK,GAAG;IACZ,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClE,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACjC,KAAK;AACL;IACA,IAAI,GAAG,GAAG;IACV,QAAQ,OAAO,IAAI,IAAI,EAAE,CAAC;IAC1B,KAAK;AACL;IACA,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE;IAC1B,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IAChC,QAAQ,MAAM,IAAI,GAAG;IACrB,YAAY,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;IAC3D,YAAY,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;IACzD,YAAY,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IACtD,YAAY,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;IACxD,YAAY,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;IAC5D,YAAY,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;IAC5D,YAAY,IAAI,CAAC,eAAe,EAAE,IAAI,KAAK,KAAK,WAAW,GAAG,GAAG,GAAG,CAAC,CAAC;IACtE,SAAS,CAAC;IACV,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACjC,KAAK;AACL;IACA,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;IAC1B,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,CAAC,IAAI,GAAG,CAAC;IACrB,YAAY,CAAC,KAAK,GAAG,CAAC;IACtB,YAAY,CAAC,GAAG,GAAG,CAAC;IACpB,YAAY,CAAC,IAAI,GAAG,CAAC;IACrB,YAAY,CAAC,MAAM,GAAG,CAAC;IACvB,YAAY,CAAC,MAAM,GAAG,CAAC;IACvB,YAAY,CAAC,WAAW,GAAG,CAAC;IAC5B,SAAS,CAAC;AACV;IACA,QAAQ,SAAS,YAAY,CAAC,MAAM,EAAE;IACtC,YAAY,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5C,YAAY,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;IAC/C,SAAS;AACT;IACA,QAAQ,MAAM,IAAI,GAAG;IACrB,YAAY,IAAI,CAAC,WAAW,EAAE;IAC9B,YAAY,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE;IACpD,YAAY,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;IACpD,YAAY,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE;IACnD,YAAY,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;IACtD,YAAY,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;IACxD,YAAY,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE;IAC7D,SAAS,CAAC;AACV;IACA,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACjC,KAAK;AACL;IACA,IAAI,KAAK,CAAC,IAAI,EAAE;IAChB,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACvD,KAAK;AACL;IACA,IAAI,eAAe,CAAC,IAAI,EAAE;IAC1B,QAAQ,OAAO;IACf,YAAY,IAAI,CAAC,WAAW,EAAE;IAC9B,YAAY,IAAI,CAAC,QAAQ,EAAE;IAC3B,YAAY,IAAI,CAAC,OAAO,EAAE;IAC1B,YAAY,IAAI,CAAC,QAAQ,EAAE;IAC3B,YAAY,IAAI,CAAC,UAAU,EAAE;IAC7B,YAAY,IAAI,CAAC,UAAU,EAAE;IAC7B,YAAY,IAAI,CAAC,eAAe,EAAE;IAClC,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,iBAAiB,CAAC,IAAI,EAAE;IAC5B,QAAQ,MAAM,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC5E;IACA,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtC;IACA,QAAQ,IAAI,KAAK,KAAK,CAAC,EAAE;IACzB,YAAY,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;IACrC,SAAS;AACT;IACA;IACA,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACxC,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE;IACnE,YAAY,OAAO,EAAE,CAAC;IACtB,SAAS;IACT,QAAQ,OAAO,EAAE,CAAC;IAClB,KAAK;IACL,CAAC,CAAC;AACF;IACA;IACA,SAAS,QAAQ,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE;IAChD,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;IACnB,IAAI,YAAY,GAAG,YAAY,IAAI,CAAC,CAAC;IACrC,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,SAAS,KAAK,WAAW,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC;IAC3E,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,YAAY,EAAE;IACnC,QAAQ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3B,KAAK,MAAM;IACX,QAAQ,YAAY,GAAG,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC;IACjD,QAAQ,IAAI,YAAY,GAAG,SAAS,CAAC,MAAM,EAAE;IAC7C,YAAY,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAC3E,SAAS;IACT,QAAQ,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9D,KAAK;IACL;;ICpUO,SAAS,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE;IAC7B,IAAI,OAAO,OAAO,IAAI,KAAK,QAAQ;IACnC,UAAU,CAAC,GAAG,IAAI,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC;IAC/C,UAAU,IAAI,IAAI,IAAI,CAAC;IACvB,CAAC;AACD;IACO,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;IACtC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;IAC7E,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;IAC5B,QAAQ,IAAI,IAAI,KAAK,WAAW,EAAE;IAClC,YAAY,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC;IAC3C,YAAY,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACrC,SAAS,MAAM,IAAI,IAAI,KAAK,WAAW,EAAE;IACzC,YAAY,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAC7C,SAAS,MAAM;IACf,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,SAAS;IACT,KAAK;IACL,IAAI,OAAO,IAAI,CAAC;IAChB,CAAC;AACD;IACO,SAAS,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;IACvD,IAAI,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAC/E;IACA,IAAI,IAAI,kBAAkB,KAAK,UAAU,EAAE;IAC3C;IACA;IACA,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IACzD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7C,QAAQ,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC;IAClC,QAAQ,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAChD,KAAK;IACL,CAAC;AACD;IACA,SAAS,mBAAmB;IAC5B,IAAI,UAAU;IACd,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,EAAE;IACN,IAAI,GAAG,GAAG,MAAM;IAChB,IAAI,KAAK,GAAG,MAAM;IAClB,EAAE;IACF,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACvD,IAAI,IAAI,MAAM,EAAE;IAChB,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;IACvB,YAAY,aAAa,EAAE,IAAI;IAC/B,YAAY,IAAI;IAChB,YAAY,EAAE;IACd,YAAY,GAAG;IACf,YAAY,KAAK,EAAE,UAAU,GAAG,KAAK;IACrC,SAAS,CAAC,CAAC;IACX,QAAQ,OAAO,UAAU,CAAC;IAC1B,KAAK;AACL;IACA,IAAI,MAAM,cAAc,GAAG,SAAS,CAAC,SAAS,EAAE;IAChD,QAAQ,aAAa,EAAE,IAAI;IAC3B,QAAQ,IAAI;IACZ,QAAQ,EAAE;IACV,QAAQ,GAAG;IACX,QAAQ,KAAK;IACb,QAAQ,QAAQ,EAAE,QAAQ;IAC1B,QAAQ,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,EAAE;IAC/B,QAAQ,QAAQ,EAAE,MAAM;IACxB,QAAQ,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC;IAC5C,KAAK,CAAC,CAAC;IACP,IAAI,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AAC3C;IACA,IAAI,OAAO,UAAU,CAAC;IACtB,CAAC;AACD;IACA,SAAS,YAAY,CAAC,IAAI,EAAE;IAC5B,IAAI,OAAO;IACX,QAAQ,IAAI,EAAE,cAAc;IAC5B,QAAQ,MAAM,EAAE,SAAS;IACzB,QAAQ,SAAS,EAAE,WAAW;IAC9B,QAAQ,UAAU,EAAE,WAAW;IAC/B,QAAQ,aAAa,EAAE,aAAa;IACpC,KAAK,CAAC,IAAI,CAAC,CAAC;IACZ,CAAC;AACD;IACA,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,KAAK;IAC/C,IAAI,IAAI,CAAC,QAAQ,EAAE;IACnB,QAAQ,QAAQ,GAAG,QAAQ,CAAC;IAC5B,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACzC,KAAK,MAAM;IACX,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,KAAK;IACL,CAAC,CAAC;AACF;IACA,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,KAAK;IACrC,IAAI,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC,CAAC;AACF;IACA,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,KAAK;IACvC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;IAChD,QAAQ,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAClD,KAAK,CAAC,CAAC;IACP,CAAC,CAAC;AACF;IACA,CAAC,CAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,KAAK;IACrD,IAAI,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;IACjD,QAAQ,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC3D,QAAQ,IAAI,eAAe,EAAE;IAC7B,YAAY,CAAC,CAAC,eAAe,GAAG,eAAe,CAAC;IAChD,YAAY,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;IACpD,SAAS;IACT,KAAK,CAAC,CAAC;IACP,CAAC,CAAC;AACF;IACA,CAAC,CAAC,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,KAAK;IACnC,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AAC9B;IACA,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;IACnC,QAAQ,OAAO,OAAO,CAAC;IACvB,KAAK;AACL;IACA,IAAI,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC,CAAC;AACF;IACA,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,KAAK;IACnC,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;IAC5C,QAAQ,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1C,KAAK;AACL;IACA,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;IAClC,QAAQ,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;IAC9B,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,SAAS;IACT,QAAQ,OAAO;IACf,KAAK;AACL;IACA,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;;ICjIc,MAAM,GAAG,CAAC;IACzB,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE;IAC7B,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvC,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,KAAK;AACL;IACA,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE;IAC9B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IACtC,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACzB,KAAK;AACL;IACA,IAAI,OAAO,GAAG;IACd,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,KAAK;AACL;IACA,IAAI,cAAc,GAAG;IACrB,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IACzC,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC;IACpD,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAClC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAClC,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;IAClE,QAAQ,IAAI,CAAC,QAAQ;IACrB,YAAY,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;IACrE,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;IACpC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;IACrE,QAAQ,IAAI,CAAC,cAAc;IAC3B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY;IAC3C,gBAAgB,IAAI,CAAC,QAAQ;IAC7B,iBAAiB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;IAChD,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE;IACpC,YAAY,KAAK,EAAE,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;IAClE,YAAY,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,SAAS,CAAC,CAAC;IACX,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE;IACxC,YAAY,KAAK,EAAE,WAAW;IAC9B,YAAY,SAAS,EAAE,IAAI,CAAC,KAAK;IACjC,SAAS,CAAC,CAAC;IACX,QAAQ,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,EAAE;IAC3C,YAAY,KAAK,EAAE,cAAc;IACjC,YAAY,SAAS,EAAE,IAAI,CAAC,KAAK;IACjC,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,eAAe,GAAG;IACtB,QAAQ,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;IAChD,YAAY,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC3C,SAAS,CAAC;IACV,QAAQ,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;IAChD,YAAY,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC3C,SAAS,CAAC;IACV,QAAQ,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;IACpD,YAAY,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C,SAAS,CAAC;IACV,QAAQ,UAAU,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;IACrD,YAAY,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChD,SAAS,CAAC;IACV,QAAQ,UAAU,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;IACnD,YAAY,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IACjD,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,IAAI,GAAG;IACX,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;IACxB,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,QAAQ,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,KAAK;AACL;IACA,IAAI,QAAQ,GAAG;IACf,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE;IACtC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACrB,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACrB,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;IAC7B,YAAY,MAAM,EAAE,IAAI,CAAC,MAAM;IAC/B,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,KAAK,EAAE,KAAK;IACxB,YAAY,SAAS,EAAE,IAAI,CAAC,SAAS;IACrC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACtD;IACA,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;IAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACnD,SAAS;IACT,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO;IACjC,QAAQ,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,EAAE;IAC/C,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACrB,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACrB,YAAY,KAAK,EAAE,IAAI,CAAC,cAAc;IACtC,YAAY,MAAM,EAAE,IAAI,CAAC,MAAM;IAC/B,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,KAAK,EAAE,cAAc;IACjC,YAAY,SAAS,EAAE,IAAI,CAAC,SAAS;IACrC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACxE,KAAK;AACL;IACA,IAAI,UAAU,GAAG;IACjB,QAAQ,SAAS,CAAC,MAAM,EAAE;IAC1B,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;IACtC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;IACvC,YAAY,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;IACrC,YAAY,KAAK,EAAE,WAAW;IAC9B,YAAY,SAAS,EAAE,IAAI,CAAC,SAAS;IACrC,SAAS,CAAC,CAAC;IACX;IACA,QAAQ,qBAAqB,CAAC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;IAClE,KAAK;AACL;IACA,IAAI,mBAAmB,GAAG;IAC1B,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO;AACjC;IACA,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IAC9B,QAAQ,MAAM,YAAY,GAAG,CAAC,CAAC;AAC/B;IACA,QAAQ,SAAS,CAAC,MAAM,EAAE;IAC1B,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC9C,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,YAAY,KAAK,EAAE,YAAY;IAC/B,YAAY,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;IACnC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,KAAK,EAAE,cAAc;IACjC,YAAY,SAAS,EAAE,IAAI,CAAC,YAAY;IACxC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,SAAS,CAAC,MAAM,EAAE;IAC1B,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,YAAY,KAAK,EAAE,YAAY;IAC/B,YAAY,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;IACnC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa;IAClC,YAAY,KAAK,EAAE,aAAa;IAChC,YAAY,SAAS,EAAE,IAAI,CAAC,YAAY;IACxC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,EAAE;IAC5D,YAAY,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,SAAS,EAAE;IACzD,gBAAgB,MAAM,EAAE,IAAI,CAAC,2BAA2B,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;IACpE,gBAAgB,KAAK,EAAE,iBAAiB;IACxC,gBAAgB,SAAS,EAAE,IAAI,CAAC,YAAY;IAC5C,aAAa,CAAC,CAAC;IACf,SAAS;IACT,KAAK;AACL;IACA,IAAI,2BAA2B,GAAG;IAClC,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;IAChD,QAAQ,OAAO;IACf,YAAY,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC;IACtC,YAAY,YAAY,CAAC,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE;IAC1D,YAAY,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC;IACtC,YAAY,YAAY,CAAC,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE;IAC1D,YAAY,YAAY,CAAC,OAAO,EAAE;IAClC,YAAY,YAAY,CAAC,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE,GAAG,IAAI;IACjE,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,IAAI,GAAG;IACX,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO;IACjC,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK;IAC7E,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;IACvC;IACA,gBAAgB,OAAO;IACvB,aAAa;AACb;IACA,YAAY,IAAI,CAAC,UAAU,EAAE,CAAC;IAC9B,YAAY,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;IACtC,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,KAAK;IAC5C,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;IACvC;IACA,gBAAgB,OAAO;IACvB,aAAa;AACb;IACA,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,UAAU,GAAG;IACjB,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO;AACjD;IACA,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM;IAC5C,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM;IAC5B,YAAY,OAAO;IACnB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ;IACvC,SAAS,CAAC;IACV,QAAQ,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM;IAC1C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC;IACxD,YAAY,OAAO;IACnB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ;IACvC,SAAS,CAAC;IACV,QAAQ,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,GAAG,QAAQ,CAAC;AACvD;IACA,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC9B,YAAY,cAAc,EAAE,IAAI,CAAC,IAAI;IACrC,YAAY,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;IACjC,YAAY,QAAQ,EAAE,QAAQ;IAC9B,YAAY,IAAI,EAAE,IAAI,CAAC,IAAI;IAC3B,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,mBAAmB,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,EAAE;IACpD,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IAC9B,QAAQ,IAAI,CAAC,EAAE;IACf;IACA,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;IAC3D,gBAAgB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC3D,aAAa,CAAC,CAAC;IACf;IACA,YAAY,MAAM,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK;IACtD,gBAAgB,OAAO,CAAC,IAAI,IAAI,CAAC;IACjC,aAAa,EAAE,CAAC,CAAC,CAAC;IAClB,YAAY,IAAI,CAAC,OAAO,EAAE;IAC1B,gBAAgB,KAAK,GAAG,IAAI,CAAC;IAC7B,gBAAgB,OAAO;IACvB,aAAa;IACb,YAAY,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,SAAS;IACT,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE;IAC/D,YAAY,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,SAAS;IACT,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,QAAQ,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACtC,QAAQ,IAAI,CAAC,2BAA2B,EAAE,CAAC;IAC3C,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,KAAK;AACL;IACA,IAAI,YAAY,GAAG;IACnB,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC;IAC5B,QAAQ,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC/E;IACA,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,cAAc,CAAC,EAAE;IACjE,YAAY,OAAO,GAAG,IAAI,CAAC;IAC3B,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;IAC9C,SAAS;AACT;IACA,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,YAAY,CAAC,EAAE;IAC7D,YAAY,OAAO,GAAG,IAAI,CAAC;IAC3B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC1C,SAAS;AACT;IACA,QAAQ,IAAI,CAAC,OAAO,EAAE,OAAO;AAC7B;IACA,QAAQ,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,aAAa,EAAE;IAChD,YAAY,IAAI,CAAC,IAAI;IACrB,YAAY,cAAc;IAC1B,YAAY,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC;IACtD,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,gBAAgB,GAAG;IACvB,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACrD,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;IAC1C,QAAQ,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;IAC/E,KAAK;AACL;IACA,IAAI,oBAAoB,GAAG;IAC3B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACrC,QAAQ,UAAU,CAAC,OAAO,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;IAChE,KAAK;AACL;IACA,IAAI,sBAAsB,GAAG;IAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IAC9B,QAAQ,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;IACxE,QAAQ,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG;IAC7C,YAAY,IAAI,CAAC,KAAK,CAAC,WAAW;IAClC,YAAY,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;IAChD,YAAY,MAAM;IAClB,SAAS,CAAC;IACV,QAAQ,MAAM,cAAc,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;IAChF,QAAQ,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG;IAC3C,YAAY,cAAc;IAC1B,YAAY,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;IACpD,YAAY,MAAM;IAClB,SAAS,CAAC;AACV;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;IAChD,KAAK;AACL;IACA,IAAI,gBAAgB,GAAG;IACvB,QAAQ,MAAM,QAAQ;IACtB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG,CAAC;IACzE,QAAQ,OAAO,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACtC,KAAK;AACL;IACA,IAAI,SAAS,GAAG;IAChB,QAAQ,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC1D,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC5C,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AACnD;IACA,QAAQ,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;IACtE,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,YAAY,CAAC;AAC7C;IACA,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IACzC,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACzE,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,IAAI,EAAE,CAAC;IAC3C,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,KAAK;AACL;IACA,IAAI,SAAS,GAAG;IAChB,QAAQ;IACR,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa;IAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;IACtC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;IACzE,UAAU;IACV,KAAK;AACL;IACA,IAAI,iBAAiB,CAAC,EAAE,EAAE;IAC1B,QAAQ,IAAI,GAAG,GAAG,EAAE;IACpB,YAAY,GAAG;IACf,YAAY,QAAQ,CAAC;AACrB;IACA,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;IACxC,YAAY,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC7D,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE;IAC3D,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC3D,SAAS,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IAChD,YAAY,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IAC9D,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE;IAC3D,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IAC5D,SAAS,MAAM;IACf,YAAY,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;IACvD,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;IAC1D,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACvD,SAAS;IACT,QAAQ,OAAO,QAAQ,CAAC;IACxB,KAAK;AACL;IACA,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;IACtC,QAAQ,KAAK,GAAG,CAAC,KAAK,CAAC;IACvB,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAC3B,YAAY,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9C,SAAS;IACT,QAAQ,OAAO,OAAO,CAAC;IACvB,KAAK;AACL;IACA,IAAI,2BAA2B,GAAG;IAClC,QAAQ,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/D,QAAQ,IAAI,CAAC,aAAa,CAAC,YAAY;IACvC,YAAY,OAAO;IACnB,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;IAC7D,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,qBAAqB,GAAG;IAC5B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI;IAC7B,YAAY,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC3D;IACA,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE;IACpD,YAAY,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvC,YAAY,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrE,SAAS,MAAM;IACf,YAAY,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,YAAY,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrE,SAAS;IACT,KAAK;AACL;IACA,IAAI,sBAAsB,GAAG;IAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;IAC9B,QAAQ,IAAI,CAAC,YAAY;IACzB,aAAa,aAAa,CAAC,cAAc,CAAC;IAC1C,aAAa,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/C,QAAQ,IAAI,CAAC,YAAY;IACzB,aAAa,aAAa,CAAC,eAAe,CAAC;IAC3C,aAAa,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAClD,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;IACpE,QAAQ,MAAM;IACd,YAAY,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;IAC9E,KAAK;AACL;IACA,IAAI,qBAAqB,GAAG;IAC5B,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IACxC,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;IACvC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC;IAC3B,SAAS;IACT,KAAK;IACL;;ICvZe,MAAM,KAAK,CAAC;IAC3B,IAAI,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE;IAC3C,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC3B,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IACnC,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B;IACA,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,KAAK;AACL;IACA,IAAI,cAAc,GAAG;IACrB,QAAQ,IAAI,OAAO;IACnB,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAC5E;IACA,QAAQ,MAAM,SAAS,GAAG;IAC1B,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;IAC3E,YAAY,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAC9E;IACA,QAAQ,OAAO,SAAS,EAAE,EAAE;IAC5B,YAAY,OAAO,IAAI,EAAE,CAAC;IAC1B,SAAS;AACT;IACA,QAAQ,MAAM,OAAO;IACrB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa;IAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU;IACzC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU;IACvE,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM;IAC1C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AACvC;IACA,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IAChF,QAAQ,MAAM,KAAK;IACnB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa;IAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC;IAC7C,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU;IACvE,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;IACxC,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AACvC;IACA,QAAQ,MAAM,gBAAgB;IAC9B,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IAClE,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;IACrD,QAAQ,MAAM,SAAS,GAAG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;IACnD,QAAQ,MAAM,OAAO,GAAG,gBAAgB,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;IAC1D,QAAQ,MAAM,MAAM,GAAG,gBAAgB;IACvC,cAAc,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW;IACpD,cAAc,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;AACrD;IACA,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;AACrB,cAAc,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;AACnC,cAAc,EAAE,MAAM,CAAC;AACvB,cAAc,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;AACpE,cAAc,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC/B;AACA;AACA,kBAAkB,CAAC,CAAC;AACpB;IACA,QAAQ;IACR,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;IACpC,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;IACnE,UAAU;IACV,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC;IAClE,YAAY,MAAM,MAAM;IACxB,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;IACxC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC;IACjD,gBAAgB,OAAO,CAAC;IACxB,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAC/E;IACA,YAAY,IAAI,CAAC,IAAI,GAAG,CAAC;AACzB,kBAAkB,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;AACvC,kBAAkB,EAAE,MAAM,CAAC;AAC3B,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC5D,kBAAkB,EAAE,IAAI,CAAC;AACzB,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;AACzE,kBAAkB,EAAE,MAAM,CAAC;AAC3B,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;AACxE,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AACnC;AACA;AACA,sBAAsB,CAAC,CAAC;IACxB,SAAS;IACT,KAAK;AACL;IACA,IAAI,IAAI,GAAG;IACX,QAAQ,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,MAAM,EAAE;IACzC,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI;IACxB,YAAY,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;IAC/C,YAAY,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;IAC3C,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,MAAM,GAAG;IACb,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD,KAAK;IACL;;IC/Fe,MAAM,KAAK,CAAC;IAC3B,IAAI,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE;IACrC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IAC7B,QAAQ,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,KAAK;AACL;IACA,IAAI,IAAI,GAAG;IACX,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC;AACjC;AACA;AACA;AACA,QAAQ,CAAC,CAAC;AACV;IACA,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;AACpB;IACA,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACzD,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC/D,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAC7D,KAAK;AACL;IACA,IAAI,IAAI,CAAC,OAAO,EAAE;IAClB,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;IACrC,YAAY,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;IACxE,SAAS;IACT,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;IAC/B,YAAY,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC;IACtC,SAAS;IACT,QAAQ,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AACtD;IACA,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;IAC9B,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,YAAY,IAAI,IAAI,6BAA6B,CAAC;IAClD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;IACzC,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACjE,SAAS,MAAM;IACf;IACA,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;IACjD,YAAY,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;IACvD,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;IACrE,SAAS;AACT;IACA;IACA,QAAQ,IAAI,aAAa,CAAC;IAC1B,QAAQ,IAAI,cAAc,YAAY,WAAW,EAAE;IACnD,YAAY,aAAa,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;IACnE,SAAS,MAAM,IAAI,cAAc,YAAY,UAAU,EAAE;IACzD,YAAY,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAC7D,SAAS;AACT;IACA,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;IACzC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;IAClC,gBAAgB,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;IACpE,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3D;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,CAAC;IAC5D,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;IAC7C,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;IAC3C,SAAS;AACT;IACA;IACA,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;IACtC,KAAK;AACL;IACA,IAAI,IAAI,GAAG;IACX,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;IACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;IACnC,KAAK;IACL;;IC5DA,MAAM,SAAS,GAAG;IAClB,IAAI,WAAW,EAAE,aAAa;IAC9B,IAAI,QAAQ,EAAE,UAAU;IACxB,IAAI,GAAG,EAAE,KAAK;IACd,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,KAAK,EAAE,OAAO;IAClB,IAAI,IAAI,EAAE,MAAM;IAChB,CAAC,CAAC;AACF;IACe,MAAM,KAAK,CAAC;IAC3B,IAAI,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;IACzC,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACpC,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACpC,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAChC;IACA,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3B,KAAK;AACL;IACA,IAAI,aAAa,CAAC,OAAO,EAAE;IAC3B,QAAQ,IAAI,WAAW,EAAE,eAAe,CAAC;AACzC;IACA;IACA,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;IACzC,YAAY,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACtD,SAAS;AACT;IACA;IACA,QAAQ,IAAI,OAAO,YAAY,WAAW,EAAE;IAC5C,YAAY,eAAe,GAAG,OAAO,CAAC;IACtC,YAAY,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACvD,SAAS,MAAM,IAAI,OAAO,YAAY,UAAU,EAAE;IAClD,YAAY,WAAW,GAAG,OAAO,CAAC;IAClC,SAAS,MAAM;IACf,YAAY,MAAM,IAAI,SAAS;IAC/B,gBAAgB,4DAA4D;IAC5E,oBAAoB,kEAAkE;IACtF,aAAa,CAAC;IACd,SAAS;AACT;IACA;IACA,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1B;IACA,YAAY,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE;IACzC,gBAAgB,SAAS,EAAE,eAAe;IAC1C,gBAAgB,KAAK,EAAE,OAAO;IAC9B,aAAa,CAAC,CAAC;IACf,SAAS,MAAM;IACf,YAAY,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IACpC,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC7C,SAAS;AACT;IACA;IACA,QAAQ,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACxD,QAAQ,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACzD;IACA,QAAQ,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACvD,QAAQ,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpD,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C;IACA;IACA,QAAQ,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3D,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC1D,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,KAAK;AACL;IACA,IAAI,aAAa,CAAC,OAAO,EAAE;IAC3B,QAAQ,MAAM,eAAe,GAAG;IAChC,YAAY,aAAa,EAAE,EAAE;IAC7B,YAAY,YAAY,EAAE,EAAE;IAC5B,YAAY,IAAI,EAAE,EAAE;IACpB,YAAY,UAAU,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACrD,YAAY,UAAU,EAAE,EAAE;IAC1B,YAAY,iBAAiB,EAAE,CAAC;IAChC,YAAY,WAAW,EAAE,CAAC;IAC1B,YAAY,OAAO,EAAE,EAAE;IACvB,YAAY,SAAS,EAAE,KAAK;IAC5B,YAAY,WAAW,EAAE,YAAY;IACrC,YAAY,aAAa,EAAE,OAAO;IAClC,YAAY,iBAAiB,EAAE,IAAI;IACnC,YAAY,QAAQ,EAAE,IAAI;IAC1B,SAAS,CAAC;IACV,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IACnE,KAAK;AACL;IACA,IAAI,WAAW,CAAC,KAAK,EAAE;IACvB;IACA,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;IAC5C;IACA,YAAY,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvD,YAAY,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnD;IACA;IACA,YAAY,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE;IACtE,gBAAgB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAChC,aAAa;AACb;IACA;IACA,YAAY,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5B;IACA;IACA,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;IAC1C,gBAAgB,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IACjD,gBAAgB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpC,gBAAgB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5D,aAAa;AACb;IACA,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE;IACzC,gBAAgB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACnE,aAAa;AACb;IACA,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;IACzC,gBAAgB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClE,aAAa;AACb;IACA;IACA;IACA,YAAY,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1E,YAAY,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE,gBAAgB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;IAClE,aAAa;AACb;IACA;IACA,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;IAC1C,gBAAgB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpC,aAAa;AACb;IACA;IACA,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;IAC7E,gBAAgB,IAAI,IAAI,GAAG,EAAE,CAAC;IAC9B,gBAAgB,IAAI,IAAI,CAAC,YAAY,EAAE;IACvC,oBAAoB,IAAI,GAAG,IAAI,CAAC,YAAY;IAC5C,yBAAyB,KAAK,CAAC,GAAG,CAAC;IACnC,yBAAyB,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7C,yBAAyB,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1C,iBAAiB;IACjB,gBAAgB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IACzC,aAAa;AACb;IACA;IACA,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;IAC1B,gBAAgB,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5C,aAAa;AACb;IACA,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,KAAK;AACL;IACA,IAAI,kBAAkB,GAAG;IACzB,QAAQ,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IACjC,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;IAClC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE;IAC1C,gBAAgB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACtE,gBAAgB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClD,aAAa;IACb,SAAS;IACT,KAAK;AACL;IACA,IAAI,OAAO,CAAC,KAAK,EAAE;IACnB,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAChC,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,KAAK;AACL;IACA,IAAI,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IACpD,QAAQ,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACrC,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;IACtB;IACA,QAAQ,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAClD,KAAK;AACL;IACA,IAAI,iBAAiB,CAAC,SAAS,EAAE;IACjC,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3C;IACA,QAAQ,IAAI,SAAS,KAAK,SAAS,CAAC,GAAG,EAAE;IACzC,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;IACnC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3C,SAAS,MAAM,IAAI,SAAS,KAAK,SAAS,CAAC,QAAQ,EAAE;IACrD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3C,SAAS,MAAM,IAAI,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE;IACxD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3C,SAAS,MAAM,IAAI,SAAS,KAAK,SAAS,CAAC,IAAI,EAAE;IACjD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;IAC5C,SAAS,MAAM,IAAI,SAAS,KAAK,SAAS,CAAC,KAAK,EAAE;IAClD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IACxC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;IAC5C,SAAS,MAAM,IAAI,SAAS,KAAK,SAAS,CAAC,IAAI,EAAE;IACjD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;IACzC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;IAC5C,SAAS;IACT,KAAK;AACL;IACA,IAAI,WAAW,GAAG;IAClB,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACjD;IACA,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;IACrC;IACA,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;IACrE,gBAAgB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IAC/C,aAAa;IACb,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE;IAC/D,gBAAgB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;IAC3C,aAAa;IACb,SAAS;AACT;IACA,QAAQ,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACxE,QAAQ,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACpE;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE;IACvE,YAAY,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3E,YAAY,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACtE,SAAS,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAClD,YAAY,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC7E,YAAY,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACvE,SAAS,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;IACjD,YAAY,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5E,YAAY,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACvE,SAAS,MAAM;IACf,YAAY,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC7E,YAAY,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACxE,SAAS;IACT,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IACxB,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC5B;IACA,QAAQ,OAAO,QAAQ,KAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE;IAC/D,YAAY,IAAI,CAAC,QAAQ,EAAE;IAC3B,gBAAgB,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9D,aAAa,MAAM;IACnB,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;IAClD,oBAAoB,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACnE,iBAAiB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAC1D,oBAAoB,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACpE,iBAAiB,MAAM;IACvB,oBAAoB,QAAQ,GAAG,UAAU,CAAC,GAAG;IAC7C,wBAAwB,QAAQ;IAChC,wBAAwB,IAAI,CAAC,OAAO,CAAC,IAAI;IACzC,wBAAwB,MAAM;IAC9B,qBAAqB,CAAC;IACtB,iBAAiB;IACjB,aAAa;IACb,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,SAAS;IACT,KAAK;AACL;IACA,IAAI,WAAW,GAAG;IAClB,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,KAAK;AACL;IACA,IAAI,MAAM,GAAG;IACb,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;IACrB,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;IACzB,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;IACzB,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3B,QAAQ,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;IACzB,QAAQ,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,KAAK;AACL;IACA,IAAI,YAAY,GAAG;IACnB,QAAQ,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACzB,QAAQ,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC/E;IACA,QAAQ,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;IAClC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE;IAChD,gBAAgB,KAAK,EAAE,KAAK;IAC5B,gBAAgB,SAAS,EAAE,IAAI,CAAC,IAAI;IACpC,aAAa,CAAC,CAAC;IACf,SAAS;IACT,KAAK;AACL;IACA,IAAI,SAAS,GAAG;IAChB,QAAQ,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpC,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,QAAQ,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpC,KAAK;AACL;IACA,IAAI,oBAAoB,GAAG;IAC3B,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACzE,QAAQ,MAAM,WAAW;IACzB,YAAY,IAAI,CAAC,OAAO,CAAC,aAAa;IACtC,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO;IAChC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;IAC3D,gBAAgB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAClC;IACA,QAAQ,SAAS,CAAC,MAAM,EAAE;IAC1B,YAAY,CAAC,EAAE,CAAC;IAChB,YAAY,CAAC,EAAE,CAAC;IAChB,YAAY,KAAK,EAAE,UAAU;IAC7B,YAAY,MAAM,EAAE,WAAW;IAC/B,YAAY,KAAK,EAAE,iBAAiB;IACpC,YAAY,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IACvC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1B,YAAY,MAAM,EAAE,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG;IAC5D,YAAY,KAAK,EAAE,MAAM;IACzB,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,cAAc,GAAG;IACrB,QAAQ,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3E,QAAQ,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5E;IACA,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACxE,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAC1E;IACA,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1E;IACA,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;IACrC,YAAY,SAAS,CAAC,MAAM,EAAE;IAC9B,gBAAgB,CAAC,EAAE,CAAC;IACpB,gBAAgB,CAAC,EAAE,KAAK;IACxB,gBAAgB,KAAK,EAAE,SAAS;IAChC,gBAAgB,MAAM,EAAE,UAAU;IAClC,gBAAgB,KAAK,EAAE,UAAU;IACjC,gBAAgB,SAAS,EAAE,UAAU;IACrC,aAAa,CAAC,CAAC;AACf;IACA,YAAY,SAAS,CAAC,MAAM,EAAE;IAC9B,gBAAgB,EAAE,EAAE,CAAC;IACrB,gBAAgB,EAAE,EAAE,KAAK,GAAG,UAAU;IACtC,gBAAgB,EAAE,EAAE,SAAS;IAC7B,gBAAgB,EAAE,EAAE,KAAK,GAAG,UAAU;IACtC,gBAAgB,KAAK,EAAE,UAAU;IACjC,gBAAgB,SAAS,EAAE,WAAW;IACtC,aAAa,CAAC,CAAC;AACf;IACA,YAAY,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IACpE,SAAS;IACT,KAAK;AACL;IACA,IAAI,gBAAgB,GAAG;IACvB,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IAC3E,QAAQ,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;IAC9D,QAAQ,SAAS,CAAC,MAAM,EAAE;IAC1B,YAAY,CAAC,EAAE,CAAC;IAChB,YAAY,CAAC,EAAE,CAAC;IAChB,YAAY,KAAK,EAAE,YAAY;IAC/B,YAAY,MAAM,EAAE,aAAa;IACjC,YAAY,KAAK,EAAE,aAAa;IAChC,YAAY,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IACvC,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,eAAe,GAAG;IACtB,QAAQ,IAAI,MAAM,GAAG,CAAC,CAAC;IACvB,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IAC3E,QAAQ,IAAI,WAAW;IACvB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;IAC3D,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9B;IACA,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;IACrC,YAAY,IAAI,UAAU,GAAG,MAAM,CAAC;IACpC;IACA,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;IACrE,gBAAgB,UAAU,IAAI,QAAQ,CAAC;IACvC,aAAa;IACb;IACA,YAAY;IACZ,gBAAgB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAC5C,gBAAgB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACnC,gBAAgB,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC;IAClC,cAAc;IACd,gBAAgB,UAAU,IAAI,QAAQ,CAAC;IACvC,aAAa;IACb;IACA,YAAY;IACZ,gBAAgB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;IAC7C,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;IAC/C,cAAc;IACd,gBAAgB,UAAU,IAAI,QAAQ,CAAC;IACvC,aAAa;AACb;IACA,YAAY,SAAS,CAAC,MAAM,EAAE;IAC9B,gBAAgB,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC3D,gBAAgB,KAAK,EAAE,UAAU;IACjC,gBAAgB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IAC3C,aAAa,CAAC,CAAC;AACf;IACA,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAC/C,gBAAgB,MAAM;IACtB,oBAAoB,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC;IACvD,wBAAwB,IAAI,CAAC,OAAO,CAAC,YAAY;IACjD,oBAAoB,EAAE,CAAC;IACvB,aAAa,MAAM;IACnB,gBAAgB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACpD,aAAa;IACb,SAAS;IACT,KAAK;AACL;IACA,IAAI,oBAAoB,GAAG;IAC3B;IACA,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;IACzC,YAAY,MAAM,CAAC;IACnB,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC;IAC9E,oBAAoB,IAAI,CAAC,OAAO,CAAC,IAAI;IACrC,gBAAgB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IAC1C,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC;AACxB;IACA,YAAY,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACpD,YAAY,MAAM,MAAM;IACxB,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;IAC/D,oBAAoB,IAAI,CAAC,KAAK,CAAC,MAAM;IACrC,gBAAgB,IAAI,CAAC,OAAO,CAAC,aAAa;IAC1C,gBAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;AACzC;IACA,YAAY,SAAS,CAAC,MAAM,EAAE;IAC9B,gBAAgB,CAAC;IACjB,gBAAgB,CAAC;IACjB,gBAAgB,KAAK;IACrB,gBAAgB,MAAM;IACtB,gBAAgB,KAAK,EAAE,iBAAiB;IACxC,gBAAgB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IAC3C,aAAa,CAAC,CAAC;IACf,SAAS;IACT,KAAK;AACL;IACA,IAAI,UAAU,GAAG;IACjB,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;IACnD,YAAY,SAAS,CAAC,MAAM,EAAE;IAC9B,gBAAgB,CAAC,EAAE,IAAI,CAAC,OAAO;IAC/B,gBAAgB,CAAC,EAAE,IAAI,CAAC,OAAO;IAC/B,gBAAgB,SAAS,EAAE,IAAI,CAAC,UAAU;IAC1C,gBAAgB,KAAK,EAAE,YAAY;IACnC,gBAAgB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IAC3C,aAAa,CAAC,CAAC;AACf;IACA,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;IACjC,gBAAgB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,EAAE;IACtD,oBAAoB,CAAC,EAAE,IAAI,CAAC,OAAO;IACnC,oBAAoB,CAAC,EAAE,IAAI,CAAC,OAAO;IACnC,oBAAoB,SAAS,EAAE,IAAI,CAAC,UAAU;IAC9C,oBAAoB,KAAK,EAAE,YAAY;IACvC,oBAAoB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;IAC/C,iBAAiB,CAAC,CAAC;AACnB;IACA;IACA,gBAAgB;IAChB,oBAAoB,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK;IAC/E,kBAAkB;IAClB,oBAAoB,WAAW,CAAC,MAAM,EAAE,CAAC;IACzC,iBAAiB;IACjB,aAAa;IACb,SAAS;IACT,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC;IAC7B,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;IAClD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAC7D,YAAY,SAAS,GAAG,IAAI,CAAC;IAC7B,YAAY,OAAO,CAAC,CAAC;IACrB,SAAS,CAAC,CAAC;IACX,QAAQ,OAAO,KAAK,CAAC;IACrB,KAAK;AACL;IACA,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE;IACtC,QAAQ,IAAI,CAAC,SAAS,EAAE;IACxB,YAAY,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACxD,SAAS;IACT,QAAQ,MAAM,SAAS,GAAG;IAC1B,YAAY,mBAAmB,EAAE,UAAU,CAAC,MAAM;IAClD,gBAAgB,IAAI;IACpB,gBAAgB,IAAI;IACpB,gBAAgB,IAAI,CAAC,OAAO,CAAC,QAAQ;IACrC,aAAa;IACb,YAAY,gBAAgB,EAAE,UAAU,CAAC,MAAM;IAC/C,gBAAgB,IAAI;IACpB,gBAAgB,IAAI;IACpB,gBAAgB,IAAI,CAAC,OAAO,CAAC,QAAQ;IACrC,aAAa;IACb,YAAY,SAAS;IACrB,gBAAgB,IAAI,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE;IACtD,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACzE,sBAAsB,EAAE;IACxB,YAAY,UAAU;IACtB,gBAAgB,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE;IACxD,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC7E,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACzE,YAAY,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC/E,YAAY,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC9E,YAAY,mBAAmB;IAC/B,gBAAgB,IAAI,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE;IACtD,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC7E,sBAAsB,EAAE;IACxB,YAAY,gBAAgB;IAC5B,gBAAgB,IAAI,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE;IACtD,sBAAsB,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE;IAC9D,0BAA0B,UAAU,CAAC,MAAM;IAC3C,8BAA8B,IAAI;IAClC,8BAA8B,OAAO;IACrC,8BAA8B,IAAI,CAAC,OAAO,CAAC,QAAQ;IACnD,2BAA2B;IAC3B,0BAA0B,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC7E,sBAAsB,EAAE;IACxB,YAAY,SAAS;IACrB,gBAAgB,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE;IACxD,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5E,sBAAsB,EAAE;IACxB,YAAY,UAAU;IACtB,gBAAgB,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE;IACxD,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5E,sBAAsB,EAAE;IACxB,YAAY,WAAW;IACvB,gBAAgB,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE;IAC9D,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5E,sBAAsB,EAAE;IACxB,YAAY,UAAU;IACtB,gBAAgB,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE;IAC9D,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5E,sBAAsB,EAAE;IACxB,SAAS,CAAC;AACV;IACA,QAAQ,MAAM,QAAQ,GAAG;IACzB,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;IAC5C,YAAY,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;IAC/C,YAAY,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE;IACpD,SAAS,CAAC;AACV;IACA,QAAQ,MAAM,KAAK,GAAG;IACtB,YAAY,mBAAmB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC;IACpE,YAAY,mBAAmB,EAAE,CAAC;IAClC,YAAY,gBAAgB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC;IACjE,YAAY,gBAAgB,EAAE,CAAC;IAC/B,YAAY,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;IACpD,YAAY,SAAS,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,IAAI,CAAC;IAC3D,YAAY,UAAU,EAAE,CAAC;IACzB,YAAY,UAAU,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC;IAC3D,YAAY,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;IACtD,YAAY,WAAW,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,IAAI,CAAC;IAC7D,YAAY,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;IACrD,YAAY,UAAU,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,IAAI,CAAC;IAC5D,SAAS,CAAC;AACV;IACA,QAAQ,OAAO;IACf,YAAY,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpE,YAAY,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpE,YAAY,OAAO,EAAE,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,YAAY,OAAO,EAAE,QAAQ,CAAC,OAAO;IACrC,YAAY,OAAO,EAAE,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1E,YAAY,OAAO,EAAE,QAAQ,CAAC,OAAO;IACrC,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,SAAS,GAAG;IAChB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;IAC7C,YAAY,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACnD,YAAY,OAAO,GAAG,CAAC;IACvB,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,WAAW,GAAG;IAClB,QAAQ,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACzB,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;IACrC,YAAY,IAAI,MAAM,GAAG,EAAE,CAAC;IAC5B,YAAY,MAAM,GAAG,IAAI,CAAC,YAAY;IACtC,iBAAiB,GAAG,CAAC,CAAC,OAAO,KAAK;IAClC,oBAAoB,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC9D,oBAAoB,IAAI,CAAC,UAAU,EAAE,OAAO;IAC5C,oBAAoB,MAAM,KAAK,GAAG,IAAI,KAAK;IAC3C,wBAAwB,IAAI;IAC5B,wBAAwB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IACpD,wBAAwB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9C,qBAAqB,CAAC;IACtB,oBAAoB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACjE,oBAAoB,OAAO,KAAK,CAAC;IACjC,iBAAiB,CAAC;IAClB,iBAAiB,MAAM,CAAC,OAAO,CAAC,CAAC;IACjC,YAAY,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACrD,SAAS;IACT,KAAK;AACL;IACA,IAAI,kBAAkB,GAAG;IACzB,QAAQ,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE;IACnC,YAAY,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK;IACvD,gBAAgB;IAChB,oBAAoB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;IAC3D,oBAAoB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;IACzD,kBAAkB;IAClB,aAAa,CAAC,CAAC;IACf,SAAS;IACT,KAAK;AACL;IACA,IAAI,SAAS,GAAG;IAChB,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;IAClE,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI;IACtC,aAAa,aAAa,CAAC,iBAAiB,CAAC;IAC7C,aAAa,YAAY,CAAC,OAAO,CAAC,CAAC;IACnC,QAAQ,IAAI,SAAS,GAAG,YAAY,EAAE;IACtC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAC1D,SAAS;IACT,KAAK;AACL;IACA,IAAI,mBAAmB,GAAG;IAC1B,QAAQ,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACvD,QAAQ,IAAI,CAAC,cAAc,EAAE,OAAO;AACpC;IACA,QAAQ,MAAM,uBAAuB,GAAG,UAAU,CAAC,IAAI;IACvD,YAAY,IAAI,CAAC,wBAAwB,EAAE;IAC3C,YAAY,IAAI,CAAC,WAAW;IAC5B,YAAY,MAAM;IAClB,SAAS,CAAC;AACV;IACA,QAAQ,MAAM,UAAU;IACxB,YAAY,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;IACxD,gBAAgB,IAAI,CAAC,OAAO,CAAC,YAAY;IACzC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;AACtC;IACA,QAAQ,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/C,KAAK;AACL;IACA,IAAI,eAAe,GAAG;IACtB,QAAQ,CAAC,CAAC,EAAE;IACZ,YAAY,IAAI,CAAC,IAAI;IACrB,YAAY,IAAI,CAAC,OAAO,CAAC,aAAa;IACtC,YAAY,yBAAyB;IACrC,YAAY,MAAM;IAClB,gBAAgB,IAAI,CAAC,YAAY,EAAE,CAAC;IACpC,gBAAgB,IAAI,CAAC,UAAU,EAAE,CAAC;IAClC,aAAa;IACb,SAAS,CAAC;IACV,KAAK;AACL;IACA,IAAI,eAAe,GAAG;IACtB,QAAQ,IAAI,WAAW,GAAG,KAAK,CAAC;IAChC,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;IAC3B,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;IAC3B,QAAQ,IAAI,gBAAgB,GAAG,KAAK,CAAC;IACrC,QAAQ,IAAI,iBAAiB,GAAG,KAAK,CAAC;IACtC,QAAQ,IAAI,aAAa,GAAG,IAAI,CAAC;IACjC,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;IACtB,QAAQ,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACtC;IACA,QAAQ,SAAS,kBAAkB,GAAG;IACtC,YAAY,OAAO,WAAW,IAAI,gBAAgB,IAAI,iBAAiB,CAAC;IACxE,SAAS;AACT;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,uBAAuB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK;IAC9E,YAAY,MAAM,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AACnE;IACA,YAAY,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;IACpD,gBAAgB,gBAAgB,GAAG,IAAI,CAAC;IACxC,aAAa,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;IAC5D,gBAAgB,iBAAiB,GAAG,IAAI,CAAC;IACzC,aAAa,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;IAClE,gBAAgB,WAAW,GAAG,IAAI,CAAC;IACnC,aAAa;AACb;IACA,YAAY,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChD;IACA,YAAY,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC;IACnC,YAAY,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC;AACnC;IACA,YAAY,aAAa,GAAG,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAChE,YAAY,MAAM,GAAG,GAAG;IACxB,gBAAgB,aAAa;IAC7B,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC;IAC9D,aAAa,CAAC;IACd,YAAY,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;IACA,YAAY,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;AACnD;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;IAClC,gBAAgB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IACtC,gBAAgB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IACtC,gBAAgB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IACtC,gBAAgB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9C,gBAAgB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACjC,aAAa,CAAC,CAAC;IACf,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,KAAK;IAC5C,YAAY,IAAI,CAAC,kBAAkB,EAAE,EAAE,OAAO;IAC9C,YAAY,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,GAAG,UAAU,CAAC;IAC9C,YAAuB,CAAC,CAAC,OAAO,GAAG,WAAW;AAC9C;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;IAClC,gBAAgB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IACtC,gBAAgB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAC1D,gBAAgB,IAAI,CAAC,UAAU,EAAE,CAAC;IAClC,gBAAgB,IAAI,gBAAgB,EAAE;IACtC,oBAAoB,IAAI,aAAa,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;IACvD,wBAAwB,GAAG,CAAC,mBAAmB,CAAC;IAChD,4BAA4B,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO;IACrD,4BAA4B,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;IAC7D,yBAAyB,CAAC,CAAC;IAC3B,qBAAqB,MAAM;IAC3B,wBAAwB,GAAG,CAAC,mBAAmB,CAAC;IAChD,4BAA4B,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO;IACrD,yBAAyB,CAAC,CAAC;IAC3B,qBAAqB;IACrB,iBAAiB,MAAM,IAAI,iBAAiB,EAAE;IAC9C,oBAAoB,IAAI,aAAa,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;IACvD,wBAAwB,GAAG,CAAC,mBAAmB,CAAC;IAChD,4BAA4B,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;IAC7D,yBAAyB,CAAC,CAAC;IAC3B,qBAAqB;IACrB,iBAAiB,MAAM,IAAI,WAAW,EAAE;IACxC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3E,iBAAiB;IACjB,aAAa,CAAC,CAAC;IACf,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK;IACpD,YAAY,IAAI,WAAW,IAAI,gBAAgB,IAAI,iBAAiB,EAAE;IACtE,gBAAgB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5E,aAAa;AACb;IACA,YAAY,WAAW,GAAG,KAAK,CAAC;IAChC,YAAY,gBAAgB,GAAG,KAAK,CAAC;IACrC,YAAY,iBAAiB,GAAG,KAAK,CAAC;IACtC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK;IAC1C,YAAY,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC1C,YAAY,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;IAClC,gBAAgB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IACtC,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;IAC1C,gBAAgB,GAAG,CAAC,YAAY,EAAE,CAAC;IACnC,gBAAgB,GAAG,CAAC,oBAAoB,EAAE,CAAC;IAC3C,aAAa,CAAC,CAAC;IACf,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,KAAK;AACL;IACA,IAAI,iBAAiB,GAAG;IACxB,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;IAC3B,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC;IAC3B,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC;IAC/B,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC;IACvB,QAAQ,IAAI,aAAa,GAAG,IAAI,CAAC;IACjC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;AACxB;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;IACxE,YAAY,WAAW,GAAG,IAAI,CAAC;IAC/B,YAAY,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC;IACnC,YAAY,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC;AACnC;IACA,YAAY,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IACnE,YAAY,MAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC5D,YAAY,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACnC;IACA,YAAY,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC;IAC9C,YAAY,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAC5B;IACA,YAAY,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;IACtC,YAAY,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;IAC5D,YAAY,aAAa,CAAC,MAAM,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;IAC7D,YAAY,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;IAC9E,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,KAAK;IAC5C,YAAY,IAAI,CAAC,WAAW,EAAE,OAAO;IACrC,YAAY,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,GAAG,UAAU,CAAC;IAC5C,YAAqB,CAAC,CAAC,OAAO,GAAG,WAAW;AAC5C;IACA,YAAY,IAAI,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE;IAC3C,gBAAgB,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC;IAC1C,aAAa;IACb,YAAY,IAAI,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE;IAC3C,gBAAgB,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC;IAC1C,aAAa;AACb;IACA,YAAY,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,CAAC;IACjD,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;IACtE,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,2BAA2B,EAAE,CAAC,CAAC;IACzE,YAAY,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC;IACvC,SAAS,CAAC,CAAC;AACX;IACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM;IACzC,YAAY,WAAW,GAAG,KAAK,CAAC;IAChC,YAAY,IAAI,EAAE,aAAa,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,OAAO;IAClE,YAAY,GAAG,CAAC,gBAAgB,EAAE,CAAC;IACnC,YAAY,GAAG,CAAC,oBAAoB,EAAE,CAAC;IACvC,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,uBAAuB,CAAC,OAAO,EAAE;IACrC,QAAQ,IAAI,GAAG,GAAG,EAAE,CAAC;IACrB,QAAQ,IAAI,UAAU,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE;IAClC,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;IAC1D,gBAAgB,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,gBAAgB,OAAO,GAAG,CAAC;IAC3B,aAAa,EAAE,EAAE,CAAC,CAAC;AACnB;IACA,YAAY,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,YAAY,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,SAAS;AACT;IACA,QAAQ,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,KAAK;AACL;IACA,IAAI,iBAAiB,CAAC,EAAE,EAAE;IAC1B,QAAQ,IAAI,GAAG,GAAG,EAAE;IACpB,YAAY,GAAG;IACf,YAAY,QAAQ,CAAC;AACrB;IACA,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;IAC1C,YAAY,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACvD,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE;IACrD,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACrD,SAAS,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAClD,YAAY,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IACxD,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE;IACrD,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IACtD,SAAS,MAAM;IACf,YAAY,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACjD,YAAY,QAAQ;IACpB,gBAAgB,GAAG;IACnB,gBAAgB,GAAG;IACnB,iBAAiB,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC;IACpD,sBAAsB,CAAC;IACvB,sBAAsB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACjD,SAAS;IACT,QAAQ,OAAO,QAAQ,CAAC;IACxB,KAAK;AACL;IACA,IAAI,YAAY,GAAG;IACnB,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK;IACxE,YAAY,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1C,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,OAAO,CAAC,KAAK,EAAE;IACnB,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;IACvC,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC;IACpD,SAAS;AACT;IACA,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAClC,YAAY,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;IACzE,SAAS;AACT;IACA,QAAQ,OAAO,KAAK,CAAC;IACrB,KAAK;AACL;IACA,IAAI,QAAQ,CAAC,EAAE,EAAE;IACjB,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;IACzC,YAAY,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;IAClC,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,OAAO,CAAC,EAAE,EAAE;IAChB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;IACvC,YAAY,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;IACtC,SAAS,CAAC,CAAC;IACX,KAAK;AACL;IACA,IAAI,UAAU,CAAC,OAAO,EAAE;IACxB,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;IACzB,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK;IAClC,gBAAgB,IAAI,CAAC,aAAa;IAClC,gBAAgB,IAAI,CAAC,OAAO,CAAC,iBAAiB;IAC9C,aAAa,CAAC;IACd,SAAS;IACT,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK;AACL;IACA,IAAI,UAAU,GAAG;IACjB,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACxC,KAAK;AACL;IACA,IAAI,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE;IAC/B,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE;IACzC,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1D,SAAS;IACT,KAAK;AACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,wBAAwB,GAAG;IAC/B,QAAQ,OAAO,IAAI,CAAC,KAAK;IACzB,aAAa,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC;IACvC,aAAa,MAAM,CAAC,CAAC,SAAS,EAAE,QAAQ;IACxC,gBAAgB,QAAQ,IAAI,SAAS,GAAG,QAAQ,GAAG,SAAS;IAC5D,aAAa,CAAC;IACd,KAAK;AACL;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,KAAK,GAAG;IACZ,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACjC,KAAK;IACL,CAAC;AACD;IACA,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC5B;IACA,SAAS,WAAW,CAAC,IAAI,EAAE;IAC3B,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrE;;;;;;;;"}