{"name": "frappe-gantt", "version": "0.6.1", "description": "A simple, modern, interactive gantt library for the web", "main": "src/index.js", "scripts": {"start": "yarn run dev", "build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "prettier": "prettier --write \"{src/*,tests/*,rollup.config}.js\"", "prettier-check": "prettier --check \"{src/*,tests/*,rollup.config}.js\""}, "repository": {"type": "git", "url": "https://github.com/frappe/gantt.git"}, "files": ["src", "dist", "README.md"], "keywords": ["gantt", "svg", "simple gantt", "project timeline", "interactive gantt", "project management"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/frappe/gantt/issues"}, "homepage": "https://github.com/frappe/gantt", "devDependencies": {"babel-preset-env": "^1.6.1", "eslint": "^4.17.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.0", "jest": "^22.2.1", "prettier": "^2.6.2", "rollup": "^2.70.2", "rollup-plugin-sass": "^1.2.12", "rollup-plugin-terser": "^7.0.2"}, "eslintIgnore": ["dist"]}