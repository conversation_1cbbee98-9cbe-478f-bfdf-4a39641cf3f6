<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Simple Gantt</title>
	<style>
		body {
			font-family: sans-serif;
			background: #ccc;
		}
		.container {
			width: 80%;
			margin: 0 auto;
		}
		/* custom class */
		.gantt .bar-milestone .bar {
			fill: tomato;
		}
		.heading {
			text-align: center;
		}
	</style>
	<link rel="stylesheet" href="dist/frappe-gantt.css" />
	<script src="dist/frappe-gantt.js"></script>
</head>
<body>
	<div class="container">
		<h2 class="heading">Interactive Gantt Chart entirely made in SVG!</h2>
		<div class="gantt-target"></div>
	</div>
	<script>
		var tasks = [
			{
				start: '2018-10-01',
				end: '2018-10-08',
				name: 'Redesign website',
				id: "Task 0",
				progress: 20
			},
			{
				start: '2018-10-03',
				end: '2018-10-06',
				name: 'Write new content',
				id: "Task 1",
				progress: 5,
				dependencies: 'Task 0'
			},
			{
				start: '2018-10-04',
				end: '2018-10-08',
				name: 'Apply new styles',
				id: "Task 2",
				progress: 10,
				dependencies: 'Task 1'
			},
			{
				start: '2018-10-08',
				end: '2018-10-09',
				name: 'Review',
				id: "Task 3",
				progress: 5,
				dependencies: 'Task 2'
			},
			{
				start: '2018-10-08',
				end: '2018-10-10',
				name: 'Deploy',
				id: "Task 4",
				progress: 0,
				dependencies: 'Task 2'
			},
			{
				start: '2018-10-11',
				end: '2018-10-11',
				name: 'Go Live!',
				id: "Task 5",
				progress: 0,
				dependencies: 'Task 4',
				custom_class: 'bar-milestone'
			},
			{
				start: '2014-01-05',
				end: '2019-10-12',
				name: 'Long term task',
				id: "Task 6",
				progress: 0
			}
		]
		var gantt_chart = new Gantt(".gantt-target", tasks, {
			on_click: task => {
				console.log(task);
			},
			on_date_change: (task, start, end) => {
				console.log(task, start, end);
			},
			on_progress_change: (task, progress) => {
				console.log(task, progress);
			},
			on_view_change: (mode) => {
				console.log(mode);
			},
			view_mode: 'Month',
			language: 'en'
		});
		console.log(gantt_chart);
	</script>
</body>
</html>