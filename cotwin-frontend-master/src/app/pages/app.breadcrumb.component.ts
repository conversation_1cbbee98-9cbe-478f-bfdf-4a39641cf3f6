import {Component} from '@angular/core';
import {BreadcrumbInfo} from "../routes/BreadcrumbInfo";
import {MenuItem} from "primeng/api";
import {Observable} from 'rxjs';
import {BreadcrumbService} from '../services/breadcrumb.service';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './app.breadcrumb.component.html',
})
export class AppBreadcrumbComponent {

  home:MenuItem = {
    url : "#/",
    icon: "pi pi-home",
    target:'_self'
  };

  breadcrumbs$: Observable<BreadcrumbInfo[]>;

  constructor(private readonly breadcrumbService: BreadcrumbService) {
    this.breadcrumbs$ = breadcrumbService.breadcrumbs$;
  }
}
