import {UserPostDto} from '../../../api/models/user/user-post-dto';

export class Post {
  public id: string = undefined;
  public body: string = undefined;
  public title: string = undefined;
  public createdTime: string = undefined;
  public downVotes: number = 0;
  public upVotes: number = 0;
  public user: UserPostDto;
  public index: number = 0;
}

export class ReplyPost {
  public postId: string = undefined;
  public postBody: string = undefined;
  public postTitle: string = undefined;
  public postCreatedTime: string = undefined;
  public userId: string = undefined;
  public userLogin: string = undefined;
  public postIndex: number = undefined;
  public threadId: string = undefined;
}
