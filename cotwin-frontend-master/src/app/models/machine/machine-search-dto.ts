import {MachinePhase, MachineStatus} from "./machine-enum";

export class MachineListDto {
  fullTextSearch: FullTextSearch;
  machineSearchDto: MachineSearchDto;
}

export class FullTextSearch {
  query: string;

  constructor(query: string) {
    this.query = query;
  }

}

export class MachineSearchDto {
  tag: String;
  phase: MachinePhase;
  status: MachineStatus | string;
  companyName: String;
  companyId: String;

  constructor() {
  }
}
