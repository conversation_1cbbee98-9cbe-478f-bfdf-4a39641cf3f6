export enum MachinePhase {
  plan = 'PlanPhase',
  build = 'BuildPhase',
  run = 'RunPhase'
}

export enum MachineStatus {
  NEWLY_ADDED = 'NEWLY_ADDED',
  CONFIGURED = 'CONFIGURED',
  ORDERED = 'ORDERED',
  SCHEDULING = 'SCHEDULING',
  ASSEMBLY = 'ASSEMBLY',
  DELIVERY = 'DELIVERY',
  IN_USE = 'IN_USE',
  PLANNED_DOWNTIME = 'PLANNED_DOWNTIME',
  UNPLANNED_DOWNTIME= 'UNPLANNED_DOWNTIME',
}

export const MachinePlanStatus = {
  [MachinePhase.plan]: [MachineStatus.NEWLY_ADDED, MachineStatus.CONFIGURED, MachineStatus.ORDERED],
  [MachinePhase.build]: [MachineStatus.SCHEDULING, MachineStatus.ASSEMBLY, MachineStatus.DELIVERY],
  [MachinePhase.run]: [MachineStatus.UNPLANNED_DOWNTIME, MachineStatus.PLANNED_DOWNTIME, MachineStatus.IN_USE]
}
