export const administratorFeatures = [
  "viewDashboard",
  "viewForum",
  "createThread",
  "createMachine",
  "viewProducts",
  "viewBuildPhaseMachineList",
  "viewRunPhaseMachineList",
  "viewPlanPhaseMachineList",
  "viewRunPhaseDashboard",
  "viewPlanPhaseConfig",
  "viewMachineDetails",
  "viewCompanyDetails",
  "viewUserDetails",
  "viewAdministrationOverview",
  "createCompany",
  "createUser",
  "viewNotifications",
  "editMachine",
  "viewBuildPhaseConfig",
  "viewRunPhaseOrderParts",
  "viewSearchResults",
  "viewServiceRequests",
  "viewKnowledge",
  "viewCotwinExplorer",
  "viewThread",
  "createPost",
  "createServiceRequest",
  "createMachineTag",
  "createMachineType",
  "createMachineOrder",
  "createMachineDelivery",
  "deleteUser",
  "deleteCompany",
  "deleteServiceRequest",
  "editServiceRequest",
  "viewProfile",
  "forumSearch",
  "viewRunPhaseConfig",
  "viewCatalog",
  "viewAllMachines"
];
export const manufacturerFeatures = [
  "viewDashboard",
  "viewForum",
  "createThread",
  "createMachine",
  "viewProducts",
  "viewBuildPhaseMachineList",
  "viewRunPhaseMachineList",
  "viewPlanPhaseMachineList",
  "viewRunPhaseDashboard",
  "viewPlanPhaseConfig",
  "viewMachineDetails",
  "createCompany",
  "createUser",
  "viewNotifications",
  "viewBuildPhaseConfig",
  "viewRunPhaseOrderParts",
  "viewSearchResults",
  "viewServiceRequests",
  "viewKnowledge",
  "viewCotwinExplorer",
  "viewThread",
  "createPost",
  "createServiceRequest",
  "createMachineOrder",
  "createMachineDelivery",
  "editMachine",
  "viewUserDetails",
  "viewCompanyDetails",
  "deleteServiceRequest",
  "editServiceRequest",
  "viewAdministrationOverview",
  "deleteUser",
  "deleteCompany",
  "createMachineTag",
  "createMachineType",
  "viewProfile",
  "forumSearch",
  "viewRunPhaseConfig",
  "viewAllMachines",
  "viewCatalog"
];
export const endCustomerFeatures = [
  "viewDashboard",
  "viewProducts",
  "viewBuildPhaseMachineList",
  "viewRunPhaseMachineList",
  "viewPlanPhaseMachineList",
  "viewMachineDetails",
  "viewNotifications",
  "viewSearchResults",
  "viewProfile",
  "viewAllMachines"
];
