import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot} from '@angular/router';
import {AuthService} from '../api/services/auth.service';
import {Observable} from 'rxjs';
import {Injectable} from '@angular/core';
import {HelperService} from '../services/helper.service';
import {AdminUserDto} from '../api/models/user/admin-user-dto';
@Injectable({
    providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  currentUser$: Observable<AdminUserDto>;
  constructor(private authService: AuthService, private helperService: HelperService, private router: Router) {
      this.currentUser$ = this.authService.getCurrentUserInfo();
    }

    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {

        const url: string = state.url;
        return this.checkLogin(url, next);
    }

    private checkLogin(url: string, route: ActivatedRouteSnapshot): boolean {
      // If token exists in the local storage, then user is logged in. Check for user role.
      if (this.authService.isLoggedIn()) {
        this.hydrateStoreState();
        return this.isAccessAllowed(route.data?.feature);
      }
      // If token not exists, clear the local storage and redirect to login page
      this.authService.redirectToUrl = url;
      this.authService.logout();
      return false;
    }

    private hydrateStoreState(){
      // Load current user into store if current user state is lost ( can happen on page refresh etc.)
      this.currentUser$.subscribe(currentUser => {
        Object.values(currentUser).every(value => {
          if(value == null){
            this.authService.loadCurrentUserIntoStoreState();
          }
          return false;
        })
      }).unsubscribe();
    }

    private isAccessAllowed(feature: string): boolean {
      const granted = this.authService.checkFeature(feature);
      if (!granted) {
        this.router.navigate(['/']);
      }
      return granted;
    }

}
