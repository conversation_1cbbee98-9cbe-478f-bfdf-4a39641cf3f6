import { Pipe, PipeTransform } from '@angular/core';
import {environment} from "../../environments/environment";

@Pipe({
  name: 'buildImageUrl'
})
export class BuildImageUrlPipe implements PipeTransform {

  transform(value: string, type: 'MACHINE' | 'SOFTWARE' | 'HARDWARE' | 'HARDWARE_MODEL'): string {
    let defaultImgUrl;
    switch (type) {
      case 'MACHINE':
        defaultImgUrl = environment.defaultImgUrl;
        break;
      case 'SOFTWARE':
        defaultImgUrl = environment.defaultSoftwareImgUrl;
        break;
      case 'HARDWARE':
        defaultImgUrl = environment.defaultHardwareImgUrl;
        break;
      case 'HARDWARE_MODEL':
        defaultImgUrl = environment.defaultHardwareModelUrl;
        break;
      default:
        defaultImgUrl = environment.defaultImgUrl;
        break;
    }
    return value ? `${environment.apiImgUrl}/${value}` : defaultImgUrl;
  }

}
