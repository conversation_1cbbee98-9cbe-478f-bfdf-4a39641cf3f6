import { Pipe, PipeTransform } from '@angular/core';
import {MachineDto} from '../api/models/machine/machine-dto';
import {TranslateService} from '@ngx-translate/core';

@Pipe({
  name: 'machineResponsibleName'
})
export class MachineResponsibleNamePipe implements PipeTransform {

  constructor(private translateService: TranslateService) {
  }

  transform(machine: MachineDto, ...args: unknown[]): unknown {
    let responsibleName = '';
    if(machine && machine.responsibleUser){
      if (machine.responsibleUser.firstName && machine.responsibleUser.lastName)
        responsibleName =
          machine.responsibleUser.firstName +
          " " +
          machine.responsibleUser.lastName;
      else if (machine.responsibleUser.firstName)
        responsibleName = machine.responsibleUser.firstName;
      //error, first name should be always present
      else
        responsibleName = this.translateService.instant(
          "machine.responsibleName.error"
        );
    }
    return responsibleName;
  }

}
