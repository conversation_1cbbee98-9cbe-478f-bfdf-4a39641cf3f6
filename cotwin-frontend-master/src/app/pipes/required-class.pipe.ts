import { Pipe, PipeTransform } from '@angular/core';
import {HelperService} from '../services/helper.service';
import {LabelInputHtmlClass} from '../models/common/LabelInputHtml.class';
import {FormGroup} from '@angular/forms';

@Pipe({
  name: 'requiredClass'
})
export class RequiredClassPipe implements PipeTransform {


  constructor(private helperService: HelperService) {
  }

  transform(form: FormGroup, field: string, fieldType?: string): LabelInputHtmlClass {
    if(fieldType === 'edit'){
      return this.helperService.getRequiredEditClass(form, field);
    }
    return this.helperService.getRequiredClass(form, field);
  }
}
