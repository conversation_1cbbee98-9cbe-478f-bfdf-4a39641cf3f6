import { Pipe, PipeTransform } from '@angular/core';
import {MachineStatusUtils} from "../utilities/machine-status-utils";
import {MachineStatus} from "../models/machine/machine-enum";

@Pipe({
  name: 'statusClassNameForStatusAndColor'
})
export class StatusClassNameForStatusAndColorPipe implements PipeTransform {

  transform(value: MachineStatus, color: string): unknown {
    return MachineStatusUtils.getStatusClassNameForStatusAndColor(value, color);
  }

}
