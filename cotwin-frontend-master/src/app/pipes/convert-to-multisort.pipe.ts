import { Pipe, PipeTransform } from '@angular/core';
import {SortMeta} from 'primeng/api';

@Pipe({
  name: 'convertToMultisort'
})
export class ConvertToMultisortPipe implements PipeTransform {

  transform(value: string[], ...args: unknown[]): SortMeta[] {
    if(!value) return undefined;
    let result: SortMeta[] = [];
    value.forEach(sort => {
      let sortArr = sort.split(',');
      result.push({field: sortArr[0], order: sortArr[1] == 'asc' ? 1:-1})
    })
    return result;
  }
}
