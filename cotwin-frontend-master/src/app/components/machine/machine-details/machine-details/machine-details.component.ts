import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {MachinePhase, MachineStatus} from '../../../../models/machine/machine-enum';
import {MachineDto} from '../../../../api/models/machine/machine-dto';
import {MachineService} from '../../../../api/services/machine.service';
import {PageHeaderData} from '../../../core/pageheader/pageheader.component';
import {MenuAction} from '../../../../models/menu/MenuAction.class';
import {TranslateService} from '@ngx-translate/core';
import {MachineStatusUtils} from '../../../../utilities/machine-status-utils';
import {MenuActionGroup} from '../../../../models/menu/MenuActionGroup.class';
import {ImageService} from '../../../../api/services/image.service';
import {Router} from '@angular/router';
import {MachineEditPageComponent} from '../../../../pages/machine/machine-edit-page/machine-edit-page.component';
import {
  MachinePlanConfigPageComponent
} from '../../../../pages/machine/plan-phase/machine-plan-config-page/machine-plan-config-page.component';
import {
  MachineBuildConfigPageComponent
} from '../../../../pages/machine/build-phase/machine-build-config-page/machine-build-config-page.component';
import {
  MachineRunDashboardPageComponent
} from '../../../../pages/machine/run-phase/machine-run-dashboard-page/machine-run-dashboard-page.component';
import {
  MachineRunOrderpartsPageComponent
} from '../../../../pages/machine/run-phase/machine-run-orderparts-page/machine-run-orderparts-page.component';
import {ServiceRequestsPageComponent} from '../../../../pages/machine/service-requests-page/service-requests-page.component';
import {CreateOrderComponent} from '../../order/create-order/create-order/create-order.component';
import {DialogService, DynamicDialogRef} from 'primeng/dynamicdialog';
import {CreateDeliveryComponent} from '../../delivery/create-delivery/create-delivery.component';
import {CustomMessageService} from '../../../../services/custom-message.service';
import { MachineRunConfigPageComponent } from 'src/app/pages/machine/run-phase/machine-run-config-page/machine-run-config-page.component';

@Component({
  selector: 'app-machine-details',
  templateUrl: './machine-details.component.html',
  providers: [DialogService],
  styleUrls: ['../../../../../assets/layout/css/machine-status.css']
})
export class MachineDetailsComponent implements OnInit {

  @Input()
  id: string;

  @Output()
  public onPageHeaderData = new EventEmitter<PageHeaderData>();

  public buildComponent;

  orderDialogRef: DynamicDialogRef;
  deliveryDialogRef: DynamicDialogRef;

  machineDetailsDto: MachineDto;
  machineStatusUtils = new MachineStatusUtils();

  public machineDto: MachineDto;

  constructor(public dialogService: DialogService, public messageService: CustomMessageService,
              private machineService: MachineService, private imageService: ImageService, private translateService: TranslateService,
              private router: Router) {
  }

  ngOnInit(): void {
    this.buildComponent = {
      machineDetails: [],
      detailsData: [],
      image: '',
      detailButtons: [],
      tags: [],
      timelineTableHeaders: [],
      timelineTableData: [],
      timelineData: [],
      timelineButtons: []
    }
    this.loadData(this.id).then(result => {
      this.machineDetailsDto = result;
      this.updateParentHeaderData();
      this.setBuildComponent()
    });

  }

  private updateParentHeaderData() {
    this.onPageHeaderData.emit(
      new PageHeaderData(
        this.machineDetailsDto.name,
        this.machineDetailsDto.description,
        [
          new MenuAction(
            this.translateService.instant('Machine.editMachine.subTitle'),
            this.translateService.instant('Machine.editMachine.title'),
            'bottom',
            'pi pi-fw pi-pencil',
            null,
            () => this.router.navigate([`/${MachineEditPageComponent.BASE_ROUTE}/${this.machineDetailsDto.id}`]),
            'editMachine'
          ),
          new MenuAction(
            '',
            this.translateService.instant('Machine.status.moveToPrevStatus'),
            'bottom',
            'pi pi-fw pi-angle-left',
            null,
            () => this.moveToPrevStatus(),
            'editMachine',
            (
              (this.machineDetailsDto.machinePhase.phase === MachinePhase.plan && this.machineDetailsDto.machinePhase.status === MachineStatus.NEWLY_ADDED)
              || (this.machineDetailsDto.machinePhase.phase === MachinePhase.run)
            ),
            'button-status-change'
          ),
          new MenuActionGroup(
            this.translateService.instant(`Machine.MachineStatus.${this.machineDetailsDto.machinePhase.phase}.${this.machineDetailsDto.machinePhase.status}`),
            this.translateService.instant('Machine.status.setStatus'),
            'bottom',
            'pi pi-fw pi-angle-down',
            'color: ' + this.machineStatusUtils.colorMap.get(MachineStatusUtils.getStatusColor(this.machineDetailsDto.machinePhase.status)) + '; width: 200px;',
            [
              {
                label: this.translateService.instant('Machine.MachinePhase.PlanPhase'),
                styleClass: 'menuModel',
                items: [
                  {
                    label: this.translateService.instant('Machine.MachineStatus.PlanPhase.NEWLY_ADDED'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.plan, MachineStatus.NEWLY_ADDED),
                    queryParams: {
                      phase: MachinePhase.plan,
                      status: MachineStatus.NEWLY_ADDED
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.PlanPhase.CONFIGURED'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.plan, MachineStatus.CONFIGURED),
                    queryParams: {
                      phase: MachinePhase.plan,
                      status: MachineStatus.CONFIGURED
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.PlanPhase.ORDERED'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.plan, MachineStatus.ORDERED),
                    queryParams: {
                      phase: MachinePhase.plan,
                      status: MachineStatus.ORDERED
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                ],
              },
              {
                label: this.translateService.instant('Machine.MachinePhase.BuildPhase'),
                items: [
                  {
                    label: this.translateService.instant('Machine.MachineStatus.BuildPhase.SCHEDULING'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.build, MachineStatus.SCHEDULING),
                    queryParams: {
                      phase: MachinePhase.build,
                      status: MachineStatus.SCHEDULING
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.BuildPhase.ASSEMBLY'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.build, MachineStatus.ASSEMBLY),
                    queryParams: {
                      phase: MachinePhase.build,
                      status: MachineStatus.ASSEMBLY
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.BuildPhase.DELIVERY'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.build, MachineStatus.DELIVERY),
                    queryParams: {
                      phase: MachinePhase.build,
                      status: MachineStatus.DELIVERY
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                ],
              },
              {
                label: this.translateService.instant('Machine.MachinePhase.RunPhase'),
                items: [
                  {
                    label: this.translateService.instant('Machine.MachineStatus.RunPhase.UNPLANNED_DOWNTIME'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.run, MachineStatus.UNPLANNED_DOWNTIME),
                    queryParams: {
                      phase: MachinePhase.run,
                      status: MachineStatus.UNPLANNED_DOWNTIME
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.RunPhase.PLANNED_DOWNTIME'),
                    icon: 'pi pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.run, MachineStatus.PLANNED_DOWNTIME),
                    queryParams: {
                      phase: MachinePhase.run,
                      status: MachineStatus.PLANNED_DOWNTIME
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                  {
                    label: this.translateService.instant('Machine.MachineStatus.RunPhase.IN_USE'),
                    icon: 'pi pi-circle-fill',
                    styleClass: this.getSelectedStatus(MachinePhase.run, MachineStatus.IN_USE),
                    queryParams: {
                      phase: MachinePhase.run,
                      status: MachineStatus.IN_USE
                    },
                    command: (event) => this.moveToSpecificStatus(event)
                  },
                ],
              },
            ],
            null,
            'editMachine'
          ),
          new MenuAction(
            '',
            this.translateService.instant('Machine.status.moveToNextStatus'),
            'bottom',
            'pi pi-fw pi-angle-right',
            null,
            () => this.moveToNextStatus(),
            'editMachine',
            this.machineDetailsDto.machinePhase.phase === MachinePhase.run,
            'button-status-change'
            )
        ]
      )
    );
  }

  private getSelectedStatus(phase: string, status: string): string {
    if (this.machineDetailsDto.machinePhase.phase === phase &&
      this.machineDetailsDto.machinePhase.status === status)
      return 'active';
    else {
      return '';
    }
  }

  protected loadData(filter: any): Promise<MachineDto> {
    return this.machineService.getMachineById(filter).toPromise().then(machine => {
      return new Promise((resolve, reject) => {
        resolve(machine);
        this.machineDto = machine;
      })
    })
  }

  private moveToNextStatus() {
    this.machineService.moveMachineToNextStatus(this.id).toPromise().then(machinePhase => {
      this.machineDetailsDto.machinePhase = machinePhase;
      this.updateParentHeaderData();
      this.setBuildComponent();
    });
  }

  private moveToPrevStatus() {
    this.machineService.moveMachineToPrevStatus(this.id).toPromise().then(machinePhase => {
      this.machineDetailsDto.machinePhase = machinePhase;
      this.updateParentHeaderData();
      this.setBuildComponent();
    });
  }

  private moveToSpecificStatus(event) {
    this.machineService.moveMachineToSpecificStatus(this.id, event.item.queryParams).toPromise().then(_ => {
      this.machineDetailsDto.machinePhase = event.item.queryParams;
      this.updateParentHeaderData();
      this.setBuildComponent();
    });
  }

  private setBuildComponent(): void{
    let imageUrl = this.imageService.getMachineImageUrl(this.machineDetailsDto.imageName);

    let timelineData = "";

    if(this.machineDetailsDto.timeList != null && this.machineDetailsDto.timeList !== ""){
      try {//data might be malformed.
        timelineData = JSON.parse(this.machineDetailsDto.timeList);
      }catch (e){
        console.log(e);
      }
    }

    let timelineTableHeaders = ['Id', this.translateService.instant('Timeline.name'), this.translateService.instant('Timeline.progress')];

    const machineDetailsData = MachineDetailsComponent.createMachineDetailsData(this.machineDetailsDto);

    let baseMachineDetails = [
      this.translateService.instant('Machine.phase'),
      this.translateService.instant('Machine.status.status'),
      this.translateService.instant('Machine.type'),
      this.translateService.instant('Machine.responsible'),
      this.translateService.instant('Machine.company'),
      this.translateService.instant('Machine.targetCompany'),
    ];
    let baseMachineDetailsData = [
      this.translateService.instant('Machine.MachinePhase.' + machineDetailsData['phase']),
      this.translateService.instant('Machine.MachineStatus.'+ machineDetailsData['phase'] + "." + machineDetailsData['status']),
      machineDetailsData['machineTypeName'],
      machineDetailsData['responsibleFullName'],
      machineDetailsData['companyName'],
      machineDetailsData['targetCompanyName'],
    ];


    if (this.machineDetailsDto.machinePhase.phase.toUpperCase() === MachinePhase.plan.toUpperCase()) {
      this.buildComponent = {
        machineDetails: baseMachineDetails,
        detailsData: baseMachineDetailsData,
        image: imageUrl,
        detailButtons: [
          {
            label: this.translateService.instant('Plan.config.title'),
            action: () => this.router.navigate([`/${MachinePlanConfigPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Plan.orderInfo'),
            action: () => {
              this.showOrderDialog(this.machineDetailsDto);
            }
          }
        ],
        tags: this.machineDetailsDto.tags ? this.machineDetailsDto.tags.map(tag => tag.name) : [],
        timelineTableHeaders: timelineTableHeaders,
        timelineData: timelineData,
        timelineButtons: ['edit']
      }
    } else if (this.machineDetailsDto.machinePhase.phase.toUpperCase() === MachinePhase.build.toUpperCase()) {
      this.buildComponent = {
        machineDetails: baseMachineDetails.concat(
          [
            this.translateService.instant('Machine.order.orderDate'),
            this.translateService.instant('Machine.productionStartDate')
          ]),
        detailsData: baseMachineDetailsData.concat(
          [
            machineDetailsData['orderDate'],
            machineDetailsData['productionStartDate']
          ]),
        image: imageUrl,
        detailButtons: [
          {
            label: this.translateService.instant('Build.config.title'),
            action: () => this.router.navigate([`/${MachineBuildConfigPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Plan.orderInfo'),
            action: () => {
              this.showOrderDialog(this.machineDetailsDto);
            }
          },
          {
            label: this.translateService.instant('Build.delivery'),
            action: () => {
              this.showDeliveryDialog(this.machineDetailsDto);
            }
          }
        ],
        tags: this.machineDetailsDto.tags ? this.machineDetailsDto.tags.map(tag => tag.name) : [],
        timelineTableHeaders: timelineTableHeaders,
        timelineData: timelineData,
        timelineButtons: ['edit']
      }
    } else if (this.machineDetailsDto.machinePhase.phase.toUpperCase() === MachinePhase.run.toUpperCase()) {
      this.buildComponent = {
        machineDetails: baseMachineDetails.concat(
          [
            this.translateService.instant('Machine.order.orderDate'),
            this.translateService.instant('Machine.productionStartDate'),
            this.translateService.instant('Machine.delivery.deliveryDate'),
          ]),
        detailsData: baseMachineDetailsData.concat(
          [
          machineDetailsData['orderDate'],
          machineDetailsData['productionStartDate'],
          machineDetailsData['deliveryDate']
          ]),
        image: imageUrl,
        detailButtons: [
          {
            label: this.translateService.instant('Plan.config.title'),
            action: () => this.router.navigate([`/${MachineRunConfigPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Run.dashboard.title'),
            action:() => this.router.navigate([`/${MachineRunDashboardPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Run.service.title'),
            action:() => this.router.navigate([`/${ServiceRequestsPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Run.orderParts.title'),
            action: () => this.router.navigate([`/${MachineRunOrderpartsPageComponent.ROUTE.replace(':id', this.machineDetailsDto.id)}`])
          },
          {
            label: this.translateService.instant('Plan.orderInfo'),
            action: () => {
              this.showOrderDialog(this.machineDetailsDto);
            }
          },
          {
            label: this.translateService.instant('Build.delivery'),
            action: () => {
              this.showDeliveryDialog(this.machineDetailsDto);
            }
          }
        ],
        tags: this.machineDetailsDto.tags ? this.machineDetailsDto.tags.map(tag => tag.name) : [],
        timelineTableHeaders: timelineTableHeaders,
        timelineData: timelineData,
        timelineButtons: ['edit']
      }
    }
  }

  showOrderDialog(machineDto: MachineDto) {
    this.orderDialogRef = this.dialogService.open(CreateOrderComponent, {
      header: this.translateService.instant('Machine.order.orderDetailsOf') + " " + machineDto.name,
      width: '40%',
      contentStyle: {"max-height": "500px", "overflow": "hidden"},
      baseZIndex: 10000,
      data: {ref: this.orderDialogRef, machineDto: machineDto},
      dismissableMask: true
    });

    this.orderDialogRef.onClose.subscribe((updatedMachineDto: MachineDto) => {
      if (updatedMachineDto) {
        machineDto = updatedMachineDto;
        this.machineDetailsDto = updatedMachineDto;
        this.setBuildComponent();
        let order = machineDto.order;
        this.messageService.info(
          (order? order.orderDate:machineDto.name),
          (this.translateService.instant('createOrder.orderInformationUpdated') + machineDto.name));
      }
    });
  }

  showDeliveryDialog(machineDto: MachineDto) {
    this.deliveryDialogRef = this.dialogService.open(CreateDeliveryComponent, {
      header: this.translateService.instant('Machine.delivery.deliveryDetailsOf') + " " + machineDto.name,
      width: '40%',
      contentStyle: {"max-height": "500px", "overflow": "hidden"},
      baseZIndex: 10000,
      data: {ref: this.deliveryDialogRef, machineDto: machineDto},
      dismissableMask: true
    });

    this.deliveryDialogRef.onClose.subscribe((updatedMachineDto: MachineDto) =>{
      if(updatedMachineDto) {
        machineDto = updatedMachineDto;
        this.machineDetailsDto = updatedMachineDto;
        this.setBuildComponent();
        let delivery = machineDto.delivery;
        this.messageService.info(
          (delivery ? delivery.deliveryDate : machineDto.name),
          (this.translateService.instant('createDelivery.deliveryInformationUpdated') + machineDto.name));
      }
    });
  }

  ngOnDestroy() {
    if (this.orderDialogRef) {
      this.orderDialogRef.destroy();
    }
    if(this.deliveryDialogRef) {
      this.deliveryDialogRef.destroy();
    }
  }

  private static createMachineDetailsData(machineDto: MachineDto){
    const phase = machineDto.machinePhase.phase;
    const status = machineDto.machinePhase.status;
    const orderDate = machineDto.order ? machineDto.order.orderDate : "";
    const companyName = machineDto.company ? machineDto.company.name : "";
    const targetCompanyName = machineDto.targetCompany ? machineDto.targetCompany.name : "";
    const responsibleFirstName = (machineDto.responsibleUser && machineDto.responsibleUser.firstName) ? machineDto.responsibleUser.firstName : "";
    const responsibleLastName = (machineDto.responsibleUser && machineDto.responsibleUser.lastName) ? machineDto.responsibleUser.lastName : "";
    const responsibleFullName = responsibleFirstName + " " + responsibleLastName;
    const machineTypeName = machineDto.machineType ? machineDto.machineType.name : "";
    const deliveryDate = machineDto.delivery ? machineDto.delivery.deliveryDate : "";
    const productionStartDate = machineDto.productionStartDate;

    return {
      'phase': phase,
      'status': status,
      'orderDate': orderDate,
      'companyName': companyName,
      'targetCompanyName': targetCompanyName,
      'responsibleFullName': responsibleFullName,
      'machineTypeName': machineTypeName,
      'deliveryDate': deliveryDate,
      'productionStartDate': productionStartDate
    };
  }

}
