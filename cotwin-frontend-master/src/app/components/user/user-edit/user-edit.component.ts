import {Component, Injector, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {AbstractEditFormComponent} from '../../core/abstracts/abstract-edit-form-component';
import {AdminUserDto} from '../../../api/models/user/admin-user-dto';
import {UserService} from '../../../api/services/user.service';
import {FormGroup, Validators} from '@angular/forms';
import {FormField} from '../../../models/common/FromField.class';
import {AuthService} from '../../../api/services/auth.service';
import {TranslateService} from '@ngx-translate/core';
import {Router} from '@angular/router';
import {ImageService} from '../../../api/services/image.service';
import {ChangePassword} from '../../../api/models/auth/change-password-dto';
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {CustomMessageService} from '../../../services/custom-message.service';

@Component({
  selector: 'app-user-edit',
  templateUrl: './user-edit.component.html',
  styleUrls: ['../../../../styles.css']
})
export class UserEditComponent extends AbstractEditFormComponent<AdminUserDto> implements OnInit, OnDestroy {

  @Input()
  public id: string;

  public activateOptions: any[];
  public langKeyOptions: any[];
  public imageFile: File = null;
  public isImageUploaded: boolean = false;
  public imageUrl: string;

  currentUser$: Observable<AdminUserDto>;
  public ngDestroyed$ = new Subject();
  public isMe: boolean;
  public isAdmin: boolean;
  public authoritiesOptions: any[];

  constructor(
    protected injector: Injector,
    private userService: UserService,
    private authService: AuthService,
    public translateService: TranslateService,
    private router: Router,
    private imageService: ImageService,
    private messageService: CustomMessageService) {
    super(injector);
  }


  public async ngOnInit(): Promise<void> {
    super.ngOnInit();

    this.currentUser$ = this.authService.getCurrentUserInfo();
    this.currentUser$
      .pipe(takeUntil(this.ngDestroyed$))
      .subscribe(currentUser => {
        if (currentUser) {
          this.isAdmin = currentUser.authorities?.includes('ROLE_ADMIN');
          this.isMe = currentUser.id == this.id;
        }
        if (this.isMe) {
          this.form.get('login').disable();
        }
      })

    this.imageUrl = this.imageService.getDefaultUserImageUrl();
    this.loadDetails(this.id);
    this.activateOptions = [{label: 'Yes', value: true}, {label: 'No', value: false}];
    this.langKeyOptions = [{
      label: this.translateService.instant('general.languages.de'),
      value: 'de'
    }, {label: this.translateService.instant('general.languages.en'), value: 'en'}];
    this.authoritiesOptions = [{
      label: this.translateService.instant('general.roles.ROLE_ADMIN'),
      value: 'ROLE_ADMIN'
    }, {
      label: this.translateService.instant('general.roles.ROLE_MANUFACTURER'),
      value: 'ROLE_MANUFACTURER'
    }, {label: this.translateService.instant('general.roles.ROLE_ENDCUSTOMER'), value: 'ROLE_ENDCUSTOMER'}];
  }

  protected buildForm(): FormGroup {
    return this.buildSimpleFormWithValidation([
      new FormField('login', this.formBuilder.control(null, Validators.required)),
      new FormField('firstName', this.formBuilder.control(null, Validators.required)),
      new FormField('lastName', this.formBuilder.control(null, Validators.required)),
      new FormField('email', this.formBuilder.control(null, [Validators.required, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')])),
      new FormField('currentPassword', this.formBuilder.control(null, /*[Validators.nullValidator, Validators.minLength(4)]*/)),
      new FormField('newPassword', this.formBuilder.control(null, /*[Validators.nullValidator, Validators.minLength(4)]*/)),
      new FormField('repeatNewPassword', this.formBuilder.control(null, /*[Validators.nullValidator, Validators.minLength(4)]*/)),
      new FormField('activated', this.formBuilder.control(null, Validators.required)),
      new FormField('langKey', this.formBuilder.control(null, /*Validators.required*/)),
      new FormField('authorities', this.formBuilder.control(null, Validators.required)),
      new FormField('customerId', this.formBuilder.control(null)),
    ]);
  }

  protected loadDetails(id: string): void {
    this.userService.getUserById(id).toPromise().then(data => {
      data.authorities = data.authorities.toString();
      this.setDetails(data);
      this.imageUrl = this.imageService.getUserImageUrl(data.imageUrl);
    });
  }

  protected internalSave = () => this.onSubmitClick();

  protected async save(): Promise<AdminUserDto> {
    const update = this.getFormValues(new AdminUserDto());
    update.authorities = update.authorities.toString().split(' ');
    update.id = this.id;
    if (this.isImageUploaded && this.isMe) {
      await this.userService.uploadUserImage(this.imageFile).toPromise().then(data => {
        this.imageUrl = this.imageService.getUserImageUrl(data.body);
        update.imageUrl = data.body;
      });
    }
    let result;
    if (this.isMe){
      result = this.authService.updateAccount(update).toPromise();
    }else {
      update.password = this.getFormValues(new ChangePassword()).newPassword;
      result = this.userService.updateUser(this.id, update).toPromise();
    }
    result.then((res) => {
      // Hydrate current user state in ngrx store after user is updated
      this.authService.loadCurrentUserIntoStoreState();
      this.messageService.success(this.translateService.instant('userManagement.createUser.updatedMessage'));
    }).catch((err) => {
      this.messageService.error(err);
    });
    return result;
  }

  protected async onSubmitClick(): Promise<void> {
    const updatePassword: ChangePassword = this.getFormValues(new ChangePassword());
    if (this.verifyPassword(updatePassword)) {
      if (this.isMe){
        await this.updatePassword(updatePassword).then(result => {
          if (result) {
            this.save();
          }
        });
      }else {
        await this.save();
      }
    }
  }

  private async updatePassword(updatePassword: ChangePassword): Promise<boolean> {
    let result = false;
    if (updatePassword.newPassword == null || updatePassword.repeatNewPassword == null) {
      result = true;
    } else {
      await this.authService.changePassword(updatePassword).toPromise().then((user) => {
        result = true;
      }).catch((err) => {
        this.messageService.error(err);
        result = false;
      });
    }
    return result;
  }

  private verifyPassword(changePassword: ChangePassword): boolean {
    let formValid = true;
    if (this.isMe && (changePassword.currentPassword == null || changePassword.currentPassword === '') &&
      (changePassword.newPassword || changePassword.repeatNewPassword)) {
      this.messageService.error(null, this.translateService.instant('Profile.errorMessage.1'));
      formValid = false;
    }
    if (changePassword.newPassword !== changePassword.repeatNewPassword) {
      this.messageService.error(null, this.translateService.instant('Profile.errorMessage.2'));
      formValid = false;
    }
    if (changePassword.newPassword?.length < 4 || changePassword.repeatNewPassword?.length < 4) {
      this.messageService.error(null, this.translateService.instant('Profile.errorMessage.3'));
      formValid = false;
    }
    return formValid;
  }

  protected delete(): Promise<boolean> {
    return this.userService.deleteUser(this.details.id).toPromise().then((res) => {
      this.messageService.success(this.translateService.instant('userList.userDeletedMessage'));
      setTimeout(() => {
        this.router.navigate(['administration']);
      }, 1000);
      return Promise.resolve(false);
    }).catch((err) => {
      this.messageService.error(err);
      return Promise.resolve(false);
    });
  }

  onFileUpload(event) {
    this.imageFile = event.files[0];
    this.isImageUploaded = true;
  }

  onFileClear() {
    this.imageFile = null;
    this.isImageUploaded = false;
  }

  removeSelectedFile() {
    this.imageFile = null;
    this.isImageUploaded = true;
    this.imageUrl = this.imageService.getUserImageUrl(null);
  }

  ngOnDestroy() {
    this.ngDestroyed$.next();
  }
}
