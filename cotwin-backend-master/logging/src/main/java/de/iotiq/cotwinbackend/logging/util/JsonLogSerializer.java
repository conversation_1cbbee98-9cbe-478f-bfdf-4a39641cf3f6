package de.iotiq.cotwinbackend.logging.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.iotiq.cotwinbackend.logging.util.LoggingUtils;

public final class JsonLogSerializer {

    private static final ObjectMapper MAPPER_IGNORE_UNKNOWN = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).
            configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .registerModule(new JavaTimeModule());


    private JsonLogSerializer() {
        throw new IllegalAccessError("Utility class");
    }

    public static <T> String fromObject(T object) {
        try {
            if (object == null)
                return null;
            return MAPPER_IGNORE_UNKNOWN.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            var str = String.valueOf(object);
            LoggingUtils.info(String.format("%s is not serializable", str));
            return str;
        }
    }
}
