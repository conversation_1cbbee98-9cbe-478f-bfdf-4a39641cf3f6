package de.iotiq.cotwinbackend.logging.annotation;

import de.iotiq.cotwinbackend.logging.util.LogType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogExecution {
    LogType logType() default LogType.ALL;
}
