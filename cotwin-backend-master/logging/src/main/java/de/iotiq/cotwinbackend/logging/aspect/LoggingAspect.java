package de.iotiq.cotwinbackend.logging.aspect;

import de.iotiq.cotwinbackend.logging.annotation.LogExecution;
import de.iotiq.cotwinbackend.logging.util.LogType;
import de.iotiq.cotwinbackend.logging.util.LoggingUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.text.StringEscapeUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@RequiredArgsConstructor
public class LoggingAspect {

    private final LogGenerator logGenerator;

    @Pointcut(
            "within(@org.springframework.stereotype.Repository *)" +
                    " || within(@org.springframework.stereotype.Service *)" +
                    " || within(@org.springframework.web.bind.annotation.RestController *)"
    )
    public void springBeanPointcut() {
    }

    @Pointcut(
            "within(de.iotiq.cotwinbackend.repository..*)" + " || within(de.iotiq.cotwinbackend.service..*)" + " || within(de.iotiq.cotwinbackend.rest..*)"
    )
    public void applicationPackagePointcut() {
    }

    @Around("applicationPackagePointcut() && springBeanPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {

        processLogging(joinPoint, joinPoint.getArgs(), LogType.REQUEST);
        Object result = joinPoint.proceed();
        processLogging(joinPoint, new Object[]{result}, LogType.RESPONSE);
        return result;

    }

    private void processLogging(JoinPoint joinPoint, Object[] parameters, LogType type) {
        Method method = null;
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            method = signature.getMethod();
            if (!isLoggingAllowed(method, type)) {
                LoggingUtils.info("{} Logger is skipping logging for log type {}", signature.toShortString(), type);
                return;
            }

            String jsonResult = logGenerator.generate(parameters);
            LoggingUtils.info(String.format("%s -> %s - %s -> %s",
                    joinPoint.getTarget().getClass().getSimpleName()
                    , signature.toShortString(),
                    type.name().toLowerCase(),
                    StringEscapeUtils.unescapeJson(jsonResult))
            );
        } catch (RuntimeException ex) {
            LoggingUtils.error(String.format("Logger could not execute %s logging for method %s",
                    type.name(),
                    getLoggableMethodName(method)), ex);
        }
    }

    private static String getLoggableMethodName(Method method) {
        return method != null ? method.getName() : "unknown method";
    }
    private static boolean isLoggingAllowed(Method method, LogType type) {
        var annotation = method.getAnnotation(LogExecution.class);
        return annotation == null || annotation.logType() == LogType.ALL
                || annotation.logType() == type;
    }
}
