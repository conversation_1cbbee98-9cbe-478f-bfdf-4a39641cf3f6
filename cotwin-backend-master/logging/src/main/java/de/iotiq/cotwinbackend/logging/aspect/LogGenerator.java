package de.iotiq.cotwinbackend.logging.aspect;
import de.iotiq.cotwinbackend.logging.util.JsonLogSerializer;
import de.iotiq.cotwinbackend.logging.util.LoggingUtils;
import org.springframework.stereotype.Component;
@Component
class LogGenerator {
    private static final String EMPTY_STRING = "";

    public String generate(Object[] parameters) {
        if (parameters.length > 1) {
            return getMultipleJsonResult(parameters);
        }
        if (parameters.length == 1) {
            return getSingleJsonResult(parameters[0]);
        }
        return EMPTY_STRING;
    }

    private static String getMultipleJsonResult(Object[] parameters) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parameters.length; i++) {
            sb.append(String.format("%d. Param : %s - ", (i + 1), getSingleJsonResult(parameters[i])));
        }
        return sb.toString();
    }

    private static String getSingleJsonResult(Object object) {
        try {
            return JsonLogSerializer.fromObject(object);
        } catch (RuntimeException ex) {//Per parameter
            LoggingUtils.error("<PERSON><PERSON> could not serialize a parameter", ex);
        }
        return null;
    }

}
