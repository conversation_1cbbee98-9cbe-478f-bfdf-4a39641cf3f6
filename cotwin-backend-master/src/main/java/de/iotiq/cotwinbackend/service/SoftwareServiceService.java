package de.iotiq.cotwinbackend.service;

import de.iotiq.cotwinbackend.domain.Company_;
import de.iotiq.cotwinbackend.domain.HardwareComponent_;
import de.iotiq.cotwinbackend.domain.SoftwareService;
import de.iotiq.cotwinbackend.domain.SoftwareService_;
import de.iotiq.cotwinbackend.exceptions.CompanyNotFoundException;
import de.iotiq.cotwinbackend.exceptions.SoftwareServiceNotFoundException;
import de.iotiq.cotwinbackend.messages.request.SoftwareServiceCreateRequest;
import de.iotiq.cotwinbackend.messages.request.SoftwareServiceSearchRequest;
import de.iotiq.cotwinbackend.messages.request.SoftwareServiceUpdateRequest;
import de.iotiq.cotwinbackend.messages.response.ImageUploadStatusInfo;
import de.iotiq.cotwinbackend.messages.response.SoftwareServiceDTO;
import de.iotiq.cotwinbackend.repository.CompanyRepository;
import de.iotiq.cotwinbackend.repository.SoftwareServiceRepository;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

import static de.iotiq.cotwinbackend.util.DomainUtil.wrapLike;
import static de.iotiq.cotwinbackend.util.NullHandlerUtil.setIfExistsOrThrow;

@Service
@Transactional
@RequiredArgsConstructor
public class SoftwareServiceService {

    private final ResourceAuthorizationService resourceAuthorizationService;
    private final SoftwareServiceRepository repository;
    private final CompanyRepository companyRepository;
    private final ModelMapper modelMapper;
    private final ImageService imageService;

    public SoftwareServiceDTO create(SoftwareServiceCreateRequest request, MultipartFile file) {

        resourceAuthorizationService.validateResourceAuthority(request.getCreatorCompanyId());
        SoftwareService software = new SoftwareService();
        mapToEntity(request, software);

        imageService.validateImageFile(file);
        ImageUploadStatusInfo imageUploadStatusInfo = imageService.uploadAndGetImageName(file);

        if(imageUploadStatusInfo.isModified()) {
            software.setImageName(imageUploadStatusInfo.getImageName());
        }

        SoftwareService saved = repository.save(software);
        return  SoftwareServiceDTO.of(saved);
    }

    public SoftwareServiceDTO update(Long id, SoftwareServiceUpdateRequest request, MultipartFile file) {

        SoftwareService software = repository.findById(id).orElseThrow(SoftwareServiceNotFoundException::new);
        resourceAuthorizationService.validateResourceAuthority(software.getCreatorCompany().getId());
        mapToEntity(request, software);

        imageService.validateImageFile(file);
        ImageUploadStatusInfo imageUploadStatusInfo = imageService.uploadAndGetImageName(file);

        if(imageUploadStatusInfo.isModified()) {
            software.setImageName(imageUploadStatusInfo.getImageName());
        }

        SoftwareService saved = repository.save(software);
        return SoftwareServiceDTO.of(saved);
    }

    public Page<SoftwareServiceDTO> getAll(SoftwareServiceSearchRequest request, Pageable pageable) {
        Page<SoftwareService> page;

        page = repository.findAll(getSearchQuery(request), pageable);

        List<SoftwareServiceDTO> dtos = page.getContent()
                .stream()
                .map(SoftwareServiceDTO::of)
                .toList();
        return new PageImpl<>(dtos, page.getPageable(), page.getTotalElements());
    }

    private Specification<SoftwareService> getSearchQuery(SoftwareServiceSearchRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicatesAnd = new ArrayList<>();

            if(request != null && StringUtils.hasText(request.getName())) {
                predicatesAnd.add(criteriaBuilder.like(criteriaBuilder.lower(root.get(SoftwareService_.NAME)), wrapLike(request.getName().toLowerCase())));
            }
            if(request != null && StringUtils.hasText(request.getCreatorCompanyName())) {
                var companyJoin = root.join(HardwareComponent_.CREATOR_COMPANY, JoinType.LEFT);
                predicatesAnd.add(criteriaBuilder.like(criteriaBuilder.lower(companyJoin.get(Company_.NAME)), wrapLike(request.getCreatorCompanyName()).toLowerCase()));
            }
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(Boolean.TRUE));
            if (!predicatesAnd.isEmpty()) {
                predicate = criteriaBuilder.and(predicatesAnd.toArray(new Predicate[0]));
            }

            return predicate;
        };
    }

    public SoftwareServiceDTO getOne(Long id) {
        SoftwareService software = repository.findById(id).orElseThrow(SoftwareServiceNotFoundException::new);
        return SoftwareServiceDTO.of(software);
    }

    public void delete(Long id) {
        repository.deleteById(id);
    }

    private void mapToEntity(SoftwareServiceCreateRequest request, SoftwareService software) {
        modelMapper.map(request, software);
        setIfExistsOrThrow(software::setCreatorCompany, request::getCreatorCompanyId, companyRepository::findById, CompanyNotFoundException::new);
    }
}
