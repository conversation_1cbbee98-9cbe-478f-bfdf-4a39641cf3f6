package de.iotiq.cotwinbackend.assetadminshell.service.impl;

import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASFindRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASFindShallowResponse;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASResponse;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASEntityFindRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.model.AASShallow;
import de.iotiq.cotwinbackend.assetadminshell.exception.NullArgumentException;
import de.iotiq.cotwinbackend.assetadminshell.service.AASEntityService;
import de.iotiq.cotwinbackend.assetadminshell.service.AASService;
import de.iotiq.cotwinbackend.assetadminshell.service.converter.AASConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public abstract class AASEntityServiceImpl<ENTITY_RESPONSE, SHALLOW_RESPONSE extends AASShallow,
        FIND_REQUEST extends AASEntityFindRequest, CREATE_REQUEST, UPDATE_REQUEST extends CREATE_REQUEST>
        implements AASEntityService<ENTITY_RESPONSE, SHALLOW_RESPONSE, FIND_REQUEST, CREATE_REQUEST,
        UPDATE_REQUEST> {

    private final AASService aasService;

    private final AASConverter<CREATE_REQUEST, AASRequest> requestConverter;
    private final AASConverter<AASResponse, ENTITY_RESPONSE> responseConverter;

    private final Supplier<ENTITY_RESPONSE> entitySupplier; // todo change name
    private final Supplier<SHALLOW_RESPONSE> shallowSupplier;

    @Override
    public Optional<ENTITY_RESPONSE> findOne(Long id) {
        NullArgumentException.check(id);

        AASResponse response = aasService.findOne(requestConverter.convertShellId(id));
        if (response != null) {
            ENTITY_RESPONSE target = entitySupplier.get();
            responseConverter.convert(response, target);
            return Optional.of(target);
        }
        return Optional.empty();
    }

    @Override
    public Page<SHALLOW_RESPONSE> find(@NotNull FIND_REQUEST filter, Pageable pageable) {
        NullArgumentException.check(filter);
        if (pageable == null) pageable = Pageable.ofSize(10);

        AASFindRequest.Filter aasFilter = AASFindRequest.Filter.builder()
                .assetUUID(requestConverter.convertAssetId(filter.getId())).build();
        AASFindRequest aasFindRequest = AASFindRequest.builder()
                .filter(aasFilter)
                .page(pageable).build();
        AASFindShallowResponse response = aasService.findAll(aasFindRequest);

        List<SHALLOW_RESPONSE> results = response.getResults()
                .stream()
                .map(aas -> {
                    SHALLOW_RESPONSE s = shallowSupplier.get();
                    s.setId(requestConverter.convertId(aas.getIdentification().getId()));
                    return s;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(results, pageable, response.getPage().getTotalElements());
    }

    @Override
    public ENTITY_RESPONSE create(CREATE_REQUEST createRequest) {
        AASRequest request = new AASRequest();
        requestConverter.convert(createRequest, request);
        AASResponse response = aasService.create(request);

        ENTITY_RESPONSE entity = entitySupplier.get();
        responseConverter.convert(response, entity);
        return entity;
    }

    @Override
    public ENTITY_RESPONSE update(UPDATE_REQUEST updateRequest) {
        AASRequest request = new AASRequest();
        requestConverter.convert(updateRequest, request);
        AASResponse response = aasService.update(request);

        ENTITY_RESPONSE machine = entitySupplier.get();
        responseConverter.convert(response, machine);
        return machine;
    }

    @Override
    public boolean delete(long id) {
        String convertedId = requestConverter.convertShellId(id);
        return aasService.delete(convertedId);
    }
}
