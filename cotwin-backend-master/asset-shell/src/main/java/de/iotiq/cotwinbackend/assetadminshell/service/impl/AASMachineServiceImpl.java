package de.iotiq.cotwinbackend.assetadminshell.service.impl;

import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASEntityFindRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASMachineCreateRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASMachineUpdateRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.model.AASMachine;
import de.iotiq.cotwinbackend.assetadminshell.domain.model.AASShallow;
import de.iotiq.cotwinbackend.assetadminshell.service.AASService;
import de.iotiq.cotwinbackend.assetadminshell.service.converter.MachineRequestConverter;
import de.iotiq.cotwinbackend.assetadminshell.service.converter.MachineResponseConverter;
import org.springframework.stereotype.Service;

@Service
public class AASMachineServiceImpl extends AASEntityServiceImpl<AASMachine, AASShallow, AASEntityFindRequest,
        AASMachineCreateRequest, AASMachineUpdateRequest> {

    public AASMachineServiceImpl(
            AASService aasService,
            MachineRequestConverter requestConverter,
            MachineResponseConverter responseConverter
    ) {
        super(aasService, requestConverter, responseConverter, AASMachine::new, AASShallow::new);
    }
}
