package de.iotiq.cotwinbackend.assetadminshell.service.converter;

import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASKind;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.external.AASSubmodel;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASMachineCreateRequest;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import static de.iotiq.cotwinbackend.assetadminshell.util.NullHandlerUtil.setIfNotNull;

@Component
public class MachineRequestConverter extends AASRequestConverter<AASMachineCreateRequest> {

    @Override
    protected String extractDescription(AASMachineCreateRequest source) {
        if (source != null) {
            return source.getDescription();
        }
        return null;
    }

    @Override
    public String getIdPrefix() {
        return MachineFields.PREFIX;
    }

    protected List<AASSubmodel> convertFieldsToSubmodels(AASMachineCreateRequest source) {
        return List.of(
                AASSubmodel.builder()
                        .identification(getIdentification(IdGroup.SUBMODEL, source.getId()))
                        .kind(AASKind.INSTANCE)
                        .submodelElements(List.of(
                                getSubmodelElement(MachineFields.NAME, source.getName()),
                                getSubmodelElement(MachineFields.DESCRIPTION, source.getDescription()),
                                getSubmodelElement(MachineFields.MACHINE_TYPE, source.getMachineType()),
                                getSubmodelElement(MachineFields.ORDER_DATE, setIfNotNull(source.getOrderDate(), LocalDate::toString)),
                                getSubmodelElement(MachineFields.DESIRED_DELIVERY_DATE, setIfNotNull(source.getDesiredDeliveryDate(), LocalDate::toString)),
                                getSubmodelElement(MachineFields.DELIVERY_SERVICES, source.getDeliveryServices()),
                                getSubmodelElement(MachineFields.PAYMENT_DATE, setIfNotNull(source.getPaymentDate(), LocalDate::toString)),
                                getSubmodelElement(MachineFields.PRODUCTION_START_DATE, setIfNotNull(source.getProductionStartDate(), LocalDate::toString)),
                                getSubmodelElement(MachineFields.DELIVERY_DATE, setIfNotNull(source.getDeliveryDate(), LocalDate::toString)),
                                getSubmodelElement(MachineFields.TIME_LIST, source.getTimeList()),
                                getSubmodelElement(MachineFields.MACHINE_PHASE, source.getMachinePhase()),
                                getSubmodelElement(MachineFields.RESPONSIBLE_USER, source.getResponsibleUser()),
                                getSubmodelElement(MachineFields.TEMPLATE, source.getTemplate()),
                                getSubmodelElement(MachineFields.TAGS, setIfNotNull(source.getTags(), Set::toString)),
                                getSubmodelElement(MachineFields.CUSTOMER, source.getCustomer()),
                                getSubmodelElement(MachineFields.SERVICE_REQUESTS, setIfNotNull(source.getServiceRequests(), Set::toString)),
                                getSubmodelElement(MachineFields.IMAGE_URL, source.getImageUrl())
                        ))
                        .build()
        );
    }
}
